<style>
    .form-control[disabled],
    .form-control[readonly],
    fieldset[disabled] .form-control {
        background-color: #484a4b;
        opacity: 1;
    }
</style>
<div id="form-pengembalian">
    <div class="container-fluid">
        <div class="content-uniq">
            <div class="row">
                <div class="col-sm-4">
                    <H4><i class="glyphicon glyphicon-log-out"></i> Form Penerimaan Barang</h4>
                </div>
                <div class="col-sm-8">
                    <div class="pull-right">
                        <h4><?= $purchase->invoice ?></h4>
                    </div>
                </div>
            </div>
            <hr style="margin-top:5px">
            <div class="row">
                <div class="col-sm-3 col-sm-offset-9">
                    <div class="form-group row mb-1">
                        <label for="tgl" class="col-sm-3 col-form-label col-form-label-sm text-right"><PERSON>gal
                            Penerimaan*</label>
                        <div class="col-sm-8">
                            <div class="input-group date">
                                <div class="input-group date">
                                    <!-- <div class="input-group date">
                                        <input type="text" class="form-control form-dark" id="tgl-confirm"
                                            name="tgl-confirm" v-model="dateConfirm.value"
                                            v-datepicker="dateConfirm.value" placeholder="Tanggal Confirm"
                                            autocomplete="false"><span class="input-group-addon"><i
                                                class="glyphicon glyphicon-th"></i></span>
                                    </div> -->
                                    <input type="text" class="form-control form-dark" id="tgl" name="tanggal"
                                        v-model="master.tgl" v-datepicker="master.tgl" placeholder="Tanggal Penerimaan"
                                        autocomplete="false"><span class="input-group-addon"><i
                                            class="glyphicon glyphicon-th"></i></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <!-- <input  type="text" class="col-sm-12 form-control form-dark input-lg input-line" placeholder="Pencarian"> -->
                    <table class="table table-report table-striped table-reprot" id="table-retur"
                        style="font-size: 14px;">
                        <thead>
                            <tr>

                                <th width="20px" class="text-center">No</th>
                                <th>Nama Produk</th>
                                <th>Satuan</th>
                                <th>Qty Stok</th>
                                <th>Harga Stok</th>
                                <th>Diterima</th>
                                <th>Sisa</th>
                                <th>Penerimaan</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-if="master.product.length ==0">
                                <td colspan="8" class="text-center">Tidak Ada Data Pada Tabel Ini</td>
                            </tr>
                            <template v-for="(i,idx) in master.product">
                                <tr :class="'tr'+idx">
                                    <td>{{idx+1}}</td>
                                    <td>{{i.product_name}}</td>
                                    <td>{{i.unit}}</td>
                                    <td>{{number(i.qty_stok.replace(".",","))}}</td>
                                    <td>{{number(i.price_stok)}}</td>
                                    <td>{{number(i.qty_arive.replace(".",","))}}</td>
                                    <td>{{number(i.sisa_confirm)}}</td>
                                    <td style="padding: 5px;"><input type="text"
                                            class="form-control form-dark text-right" :disabled="i.sisa_confirm<=0"
                                            v-model="master.product[idx].form.qtyArive"
                                            v-number="master.product[idx].form.qtyArive" placeholder="Qty Penerimaan"
                                            @blur="onQtyArive(idx)" />
                                        <span>{{master.product[idx].form.error}}</span>
                                    </td>
                                    <!-- <td v-if="(i.qty_stok-i.qty_arive)>0"><button class="btn btn-xs btn-success" @click="getHistory(i.purchase_products_id,idx,i.sisa_qty,i.price_stok)" title="Penerimaan" :disabled="i.show==true"><i class="glyphicon glyphicon-inbox"></i> Penerimaan</button> </td>
                                    <td v-else><button class="btn btn-xs btn-primary" @click="getHistory(i.purchase_products_id,idx,i.sisa_qty)" title="Detail" :disabled="i.show==true"><i class="glyphicon glyphicon-list"></i> Detail</button> </td> -->
                                </tr>
                                <!-- <tr  style="background-color: #444545" v-if="i.show==true">
                                    <td colspan="8">
                                        <div class="row">
                                            <div class="col-sm-11 col-sm-offset-1">
                                                <small class="text-bld">History Penerimaan Barang*</small>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-11 col-sm-offset-1">
                                                <table class="table table-striped table-report">
                                                    <thead>
                                                        <tr>
                                                            <th style="background-color: #2b2c2d;" width="20%">Tanggal</th>
                                                            <th style="background-color: #2b2c2d;">Jumlah Pembelian</th>
                                                            <th style="background-color: #2b2c2d;" width="20%">Diterima</th>
                                                            <th style="background-color: #2b2c2d;">Sisa</th>
                                                            <th style="background-color: #2b2c2d;">User</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr  v-if="i.history.length>0" v-for="(j,index) in i.history">
                                                            <td>{{date(j.date_created)}}</td>
                                                            <td>{{number(j.qty)}}</td>
                                                            <td>{{number(j.qty_arive)}}</td></td>
                                                            <td>{{number(j.qty-j.qty_arive)}}</td>
                                                            <td>{{j.user}}</td>
                                                        </tr>
                                                        <tr v-if="i.history.length==0">
                                                            <td colspan="5" class="text-center">Belum Ada Pengembalian Barang</td>
                                                        </tr>
                                                        <tr >
                                                            <td></td>
                                                            <td></td>
                                                            <td style="padding: 5px;"><input type="text" class="form-control form-dark input-sm text-right" :disabled="i.sisa_confirm<=0" v-model="master.product[idx].form.qtyArive" v-number="master.product[idx].form.qtyArive" placeholder="Qty Penerimaan"/>
                                                            <small>{{master.product[idx].form.error}}</small>
                                                            </td>
                                                            <td></td>
                                                            <td></td>
                                                        </tr>  
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>   
                                    </td>
                                </tr> -->
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="row">
                <div class="modal-footer">
                    <a href="<?= base_url() ?>purchase/purchasing_product">
                        <button type="button" class="btn btn-default">Batal</button>
                    </a>
                    <button type="button" class="btn btn-primary" @click="onSubmit()" id="btn-confirm">Simpan</button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    $(function () {

        // $('#tgl').datepicker({
        //     autoclose: true,
        //     format: "dd-mm-yyyy",
        //     startDate: milisToLocal("<?= $purchase->date_purchase ?>", "DD-MM-YYYY"),
        //     todayHighlight: true,
        //     endDate: "today"
        // });
        let minimalDate = milisToLocal("<?= $purchase->date_purchase ?>", "DD-MM-YYYY hh:mm");
        $('#tgl').datetimepicker({
            autoclose: true,
            dateFormat: "y-m-d h:i",
            format: "dd-mm-yyyy h:i",
            startDate: minimalDate,
            todayHighlight: true,
        });

        // $('#tgl').datepicker({
        //     autoclose: true,
        //     format: "dd-mm-yyyy",
        //     // startDate: new Date(),
        //     todayHighlight: true,
        //     endDate: "today"
        // });
    });

    var vm = new Vue({
        el: "#form-pengembalian",
        data: {
            master: {
                tgl: '',
                product: []
            }
        },
        methods: {
            appendForm(id, idx, sisaQty, hpp, product_detail_id) {
                var form = {
                    qtyArive: '',
                    qty: sisaQty,
                    id_detail: id,
                    hpp: hpp,
                    product_detail_id: product_detail_id,
                    error: ''
                }
                this.master.product[idx]['form'] = form
            },
            getProduct() {
                $.ajax({
                    type: "get",
                    url: "<?= base_url() ?>purchasing/purchasing_product/detail_purchase",
                    data: { id: <?= $purchase->purchase_id ?> },
                    dataType: "json",
                    beforeSend() {
                        loading.show()
                    },
                    success: function (res) {
                        loading.hide()
                        vm.master.product = res
                        for (const key in res) {
                            vm.appendForm(res[key].purchase_products_id, key, res[key].sisa_confirm, res[key].price_stok, res[key].products_fkid)
                        }

                    },
                    error(err) {
                        loading.hide()
                        Alert.error("error", "Gagal Memuat Produk")
                        console.log(err);
                    }

                });
            },
            number(param) {
                return number(param)
            },
            date(param) {
                return moment.unix(param / 1000).local().format("DD/MM/YYYY HH:mm:ss")
            },
            getHistory(id, idx, sisaQty, hpp) {
                vm.master.product[idx].history = []
                $.ajax({
                    type: "get",
                    url: "<?= base_url() ?>purchasing/purchasing_product/confirm_history",
                    data: { id: id },
                    dataType: "json",
                    beforeSend() {
                        loading.show()
                    },
                    success: function (res) {
                        loading.hide();
                        vm.master.product[idx].show = true
                        if (res.length > 0) {
                            vm.master.product[idx].history = res
                        } else {
                            vm.master.product[idx].history = []
                        }
                        console.log(sisaQty);
                        vm.appendForm(id, idx, sisaQty, hpp)
                    },
                    error(err) {
                        Alert.error("Error", "Gagal Memuat Data History")
                        console.log(err);
                    }
                });
            },
            onSubmit() {
                // maping data
                // validasi date
                if (this.master.tgl == '') {
                    return Alert.error('warning', 'tanggal penerimaan belum diisi');
                }
                var data = []
                for (var key in this.master.product) {
                    if (this.master.product[key].form.qtyArive > this.master.product[key].form.qty) {
                        this.master.product[key].form.error = "Melebihi Jumlah Sisa"
                        Alert.warning("oops!", "Jumlah penerimaan melebihi jumlah pembelian")
                        return
                    }
                    this.master.product[key].form.qtyArive = this.master.product[key].form.qtyArive.replace(",", '.');
                    data.push(vm.master.product[key].form)
                }

                // return console.log(data);        

                data['invoice'] = "<?= $purchase->invoice ?>"
                data['outlet_id'] = "<?= $purchase->outlet_fkid ?>"
                data['purchase_id'] = "<?= $purchase->outlet_fkid ?>"

                var param = {
                    data: data,
                    date: new Date(reverseDate(this.master.tgl)).getTime(),
                    invoice: "<?= $purchase->invoice ?>",
                    outlet_id: "<?= $purchase->outlet_fkid ?>",
                    purchase_id: "<?= $purchase->outlet_fkid ?>"
                }
                // console.log(data);
                //ajax insert
                $.ajax({
                    type: "post",
                    url: "<?= base_url() ?>purchasing/purchasing_product/arive_save",
                    data: param,
                    dataType: "json",
                    beforeSend() {
                        loading.show()
                        $("#btn-confirm").button('loading')
                    },
                    success: function (res) {
                        loading.hide()
                        Alert.success("Success", "Data Berhasil Disimpan");
                        location.href = "<?= base_url() ?>purchase/purchasing_product"
                    },
                    error(err) {
                        loading.hide();
                        Alert.error("Error", "Periksa Kembali Inputan Anda");
                        console.log(err);
                    }
                });
            },
            onQtyArive(idx) {
                if (this.master.product[idx].form.qtyArive > this.master.product[idx].form.qty) {
                    this.master.product[idx].form.error = "Melebihi Jumlah Sisa"
                } else {
                    this.master.product[idx].form.error = ""
                }
            }
        },
        watch: {

        },
        mounted() {
            this.getProduct()
        },
    })
</script>