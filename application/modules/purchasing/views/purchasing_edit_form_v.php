<style>
    .select2-search {
        background-color: #2d2d2d;
    }

    .select2-search input {
        background-color: #2d2d2d;
    }

    .select2-results {
        background-color: #2d2d2d
    }

    /* .select2-choice { background-color: #2d2d2d !important; } */
    .select2-container--default .select2-selection--single .select2-selection__rendered {
        color: #fff;
    }

    .select2-container--default .select2-selection--single {
        background-color: #2d2d2d;
        color: #fff;
        border-color: #6c757d;
    }

    .select2-container--default .select2-results__option[aria-selected=true] {
        background-color: #424242;
    }

    .table>tbody>tr>td {
        padding: 1px;
    }

    .select2-container--default.select2-container--disabled .select2-selection--single {
        background-color: #464545;
    }
</style>
<div id="form-purchase">
    <div class="container-fluid">
        <div class="content-uniq">
            <div class="row">
                <div class="col-sm-4">
                    <span><i class="fa fa-gears"></i> Form Pembelian</span>
                </div>
                <div class="col-sm-8">

                </div>
            </div>
            <hr>
            <div class="row">
                <form id="form-menu" novalidate>
                    <div class="col-sm-6">
                        <div class="form-group row mb-1">
                            <label for="invoice"
                                class="col-sm-3 col-form-label col-form-label-sm text-right">Invoice*</label>
                            <div class="col-sm-8">
                                <input type="text" id="invoice"
                                    :class="['form-control form-dark', 'input-sm', form.inputs.invoice.error ? 'is-invalid' : '']"
                                    name="invoice" v-model="form.inputs.invoice.value" placeholder="[Auto]" required />
                                <div class="invalid-feedback">{{ form.inputs.invoice.error }}</div>
                            </div>
                        </div>
                        <div class="form-group row mb-1">
                            <label for="outlet" class="col-sm-3 col-form-label col-form-label-sm text-right">
                                Outlet*
                            </label>
                            <div class="col-sm-8">
                                <select id="outlet" :class="['form-control form-dark','select2select']" name="outlet"
                                    v-model="form.inputs.outlet.value" v-select="form.inputs.outlet.value"
                                    style="width:100%" data-placeholder="-- pilih outlet --">
                                    <?php foreach ($form_select_outlet as $a): ?>
                                        <option value="<?= $a->outlet_id ?>"><?= htmlentities($a->name) ?></option>
                                    <?php endforeach ?>

                                </select>
                                <small class="invalid-feedback">{{ form.inputs.outlet.error }}</small>
                            </div>
                        </div>
                        <div class="form-group row mb-1">
                            <label for="shift" class="col-sm-3 col-form-label col-form-label-sm text-right">
                                Shift*
                            </label>
                            <div class="col-sm-8">
                                <select id="shift" :class="['form-control form-dark','select2select']" name="shift"
                                    v-model="form.inputs.shift.value" v-select="form.inputs.shift.value"
                                    style="width:100%" data-placeholder="-- pilih shift --">
                                    <?php foreach ($form_select_shift as $a): ?>
                                        <option value="<?= $a->shift_id ?>"><?= htmlentities($a->name) ?></option>
                                    <?php endforeach ?>
                                </select>
                                <small class="invalid-feedback">{{ form.inputs.shift.error }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="form-group row mb-1">
                            <label for="supplier" class="col-sm-2 col-form-label col-form-label-sm text-right">
                                Supplier*
                            </label>
                            <div class="col-sm-8">
                                <select id="supplier" :class="['form-control form-dark', 'select2select']"
                                    name="supplier" v-model="form.inputs.supplier.value"
                                    v-select="form.inputs.supplier.value" style="width:100%"
                                    data-placeholder="-- pilih supplier --">
                                    <?php foreach ($form_select_supplier as $a): ?>
                                        <option value="<?= $a->supplier_id ?>"><?= htmlentities($a->name) ?></option>
                                    <?php endforeach ?>
                                </select>
                                <small class="invalid-feedback">{{ form.inputs.supplier.error }}</small>
                            </div>
                        </div>
                        <div class="form-group row mb-1">
                            <label for="tgl"
                                class="col-sm-2 col-form-label col-form-label-sm text-right">Tanggal*</label>
                            <div class="col-sm-8">
                                <div class="input-group date">
                                    <?php if ($detail_access['change_date']) { ?>
                                        <div class="input-group date">
                                            <input type="text" class="form-control form-dark" id="tgl" disabled
                                                name="tanggal" v-model="form.inputs.tgl.value"
                                                v-datepicker="form.inputs.tgl.value" placeholder="Tanggal Transfer"
                                                autocomplete="false"><span class="input-group-addon"><i
                                                    class="glyphicon glyphicon-th"></i></span>
                                        </div>
                                    <?php } else { ?>
                                        <span class="form-control input-sm form-dark">{{form.inputs.tgl.value}}</span>
                                    <?php } ?>
                                </div>
                                <small class="invalid-feedback">{{ form.inputs.tgl.error }}</small>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <br>
            <div class="row">
                <div class="col-sm-12">
                    <button class="btn btn-primary btn-sm pull-right" @click="onSetting()"><i
                            class="fa fa-cog"></i></button>
                    <span class="pull-right" style="margin-right: 20px;" v-if="setting.hargaTermasukPajak">*Harga
                        Termasuk Pajak </span>
                    <table class="table table-primary table-striped table-report" id="table-purchase">
                        <thead>
                            <tr>
                                <th rowspan="2" class="text-center" style="vertical-align: middle;">No</th>
                                <th rowspan="2" class="text-center" style="vertical-align: middle;">Produk</th>
                                <th colspan="3" class="text-center" style="vertical-align: middle;">Nota</th>
                                <th rowspan="2" class="text-center" style="vertical-align: middle;">Sub Total</th>
                                <th rowspan="2" v-if="setting.disc" class="text-center" style="vertical-align: middle;">
                                    Diskon</th>
                                <th rowspan="2" v-if="setting.pajak" class="text-center"
                                    style="vertical-align: middle;">Pajak</th>
                                <th rowspan="2" v-if="setting.total" class="text-center"
                                    style="vertical-align: middle;">Total</th>
                                <th colspan="3" class="text-center" class="text-center" style="vertical-align: middle;">
                                    Stok</th>
                                <th rowspan="2" class="text-center" style="vertical-align: middle;">Diterima</th>
                                <th rowspan="2" v-if="setting.keterangan" class="text-center"
                                    style="vertical-align: middle;">Keterangan</th>
                                <th rowspan="2" class="text-center" style="vertical-align: middle;">Retur</th>
                                <th rowspan="2" class="text-center" style="vertical-align: middle;">#</th>
                            </tr>


                            <tr>
                                <th class="text-center" style="vertical-align: middle;">Unit</th>
                                <th class="text-center" style="vertical-align: middle;">Qty</th>
                                <th class="text-center" style="vertical-align: middle;">Harga</th>
                                <th v-if="setting.unitStock" class="text-center" style="vertical-align: middle;">Unit
                                </th>
                                <th v-if="setting.qtyStock" class="text-center" style="vertical-align: middle;">Qty</th>
                                <th v-if="setting.hargaStock" class="text-center" style="vertical-align: middle;">Harga
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="text-center" v-if="form.inputs.outlet.value==''">
                                <td colspan="13">-- Pilih Outlet Dahulu --</td>
                            </tr>
                            <tr v-for="(i,idx) in form.detail" v-else>
                                <td class="text-center">{{idx+1}}</td>
                                <td>
                                    <span v-if="form.po"
                                        class="form-control form-dark input-sm">{{form.detail[idx].product_id.product_name}}</span>

                                    <select v-else :onchange="setUnitStock(idx)"
                                        :class="['form-control form-dark form-product'+idx]"
                                        v-model="form.detail[idx].product_id.value"
                                        v-select="form.detail[idx].product_id.value" :id="'product_id'+idx"
                                        style="width:100%" data-placeholder="-- Pilih Produk --">
                                        <option v-for="i,idx in product" :value="i.product_detail_id"
                                            :data-id="i.product_detail_id" :data-unit_id="i.unit_id"
                                            :data-unit="i.unit_name" :data-last_price="i.last_price"
                                            :data-product_id="i.product_id">{{i.product_name}}</option>
                                    </select>

                                    <small class="invalid-feedback">{{ form.detail[idx].product_id.error }}</small>
                                </td>
                                <td>
                                    <select :class="['form-control form-dark form-product unit'+idx]"
                                        v-model="form.detail[idx].unit_nota.value"
                                        v-select="form.detail[idx].unit_nota.value" style="width:100%"
                                        data-placeholder="-- Pilih Unit --" @change="onChangeUnit(idx)"
                                        :disabled="form.detail[idx].unit_nota.disabled" :id="'unit-nota'+idx">
                                        <?php foreach ($form_select_unit_model as $a): ?>
                                            <option value="<?= $a->unit_id ?>"><?= htmlentities($a->name) ?></option>
                                        <?php endforeach ?>
                                    </select>
                                    <small class="invalid-feedback">{{ form.detail[idx].unit_nota.error }}</small>
                                </td>
                                <td>
                                    <input type="text"
                                        :class="['form-control text-right form-dark input-number', 'input-sm', form.detail[idx].qty_nota.error ? 'is-invalid' : '']"
                                        v-model="form.detail[idx].qty_nota.value" placeholder="" required
                                        @blur="subTotalDetail(idx)" :id="'qty-nota'+idx" onfocus="this.select()" />
                                    <small class="invalid-feedback">{{ form.detail[idx].qty_nota.error }}</small>
                                </td>
                                <td>
                                    <input type="text"
                                        :class="['form-control text-right form-dark', 'input-sm', form.detail[idx].harga_nota.error ? 'is-invalid' : '']"
                                        v-model="form.detail[idx].harga_nota.value"
                                        v-money="form.detail[idx].harga_nota.value" placeholder="" required
                                        @blur="subTotalDetail(idx)" :id="'harga-nota'+idx" onfocus="this.select()" />
                                    <small class="invalid-feedback">{{ form.detail[idx].harga_nota.error }}</small>
                                </td>
                                <td>
                                    <input type="text"
                                        :class="['form-control text-right form-dark', 'input-sm', form.detail[idx].sub_total.error ? 'is-invalid' : '']"
                                        v-model="form.detail[idx].sub_total.value"
                                        v-money="form.detail[idx].sub_total.value" placeholder="" required
                                        @blur="priceNota(idx)" :id="'sub-total'+idx" onfocus="this.select()" />
                                    <small class="invalid-feedback">{{ form.detail[idx].sub_total.error }}</small>
                                </td>
                                <td v-if="setting.disc">
                                    <span
                                        @click="onDiscDetail(idx,form.detail[idx].disc.type,form.detail[idx].disc.value)"
                                        class="form-control form-dark input-sm text-right"
                                        :id="'diskon'+idx">{{numberFormat(form.detail[idx].disc.value)}}
                                        {{form.detail[idx].disc.type=='percent'?'%':''}}</span>
                                </td>
                                <td v-if="setting.pajak">
                                    <select :class="['form-control form-dark form-product pajak'+idx]"
                                        v-model="form.detail[idx].pajak.value" v-select="form.detail[idx].pajak.value"
                                        style="width:100%" data-placeholder="-- Pajak --" :onchange="pajakDetail(idx)"
                                        :id="'tax-id'+idx">
                                        <?php foreach ($form_select_tax as $a): ?>
                                            <option value="<?= $a->gratuity_id ?>" data-type="<?= $a->tax_type ?>"
                                                data-nominal="<?= $a->jumlah ?>"><?= htmlentities($a->name) ?></option>
                                        <?php endforeach ?>
                                    </select>
                                    <small class="invalid-feedback">{{ form.detail[idx].pajak.error }}</small>
                                </td>
                                <td v-if="setting.total">
                                    <span
                                        class="form-control form-dark input-sm">{{form.detail[idx].total.value}}</span>
                                </td>
                                <td v-if="setting.unitStock">
                                    <span
                                        class="form-control form-dark input-sm">{{form.detail[idx].unit_stock.title}}</span>
                                </td>
                                <td v-if="setting.qtyStock">
                                    <input type="text"
                                        :class="['form-control form-dark text-right input-number', 'input-sm', form.detail[idx].qty_stock.error ? 'is-invalid' : '']"
                                        v-model="form.detail[idx].qty_stock.value"
                                        v-money="form.detail[idx].qty_stock.value" placeholder="" required
                                        @change="hargaStock(idx)" :id="'qty-stock'+idx" onfocus="this.select()" />
                                    <small class="invalid-feedback">{{ form.detail[idx].qty_stock.error }}</small>
                                </td>
                                <td v-if="setting.hargaStock">
                                    <span
                                        class="form-control form-dark input-sm">{{form.detail[idx].harga_stock.value}}</span>
                                </td>
                                <td>
                                    <input type="text"
                                        :class="['form-control form-dark text-right input-number', 'input-sm', form.detail[idx].diterima.error ? 'is-invalid' : '']"
                                        v-model="form.detail[idx].diterima.value"
                                        v-money="form.detail[idx].diterima.value" placeholder="" required
                                        :id="'diterima'+idx" onfocus="this.select()" @blur="validateArive(idx)" />
                                    <small class="invalid-feedback">{{ form.detail[idx].diterima.error }}</small>
                                </td>
                                <td v-if="setting.keterangan">
                                    <textarea type="text"
                                        :class="['form-control form-dark text-right', 'input-sm', form.detail[idx].keterangan.error ? 'is-invalid' : '']"
                                        v-model="form.detail[idx].keterangan.value" placeholder="" required
                                        :id="'keterangan'+idx" onfocus="this.select()"> </textarea>
                                    <small class="invalid-feedback">{{ form.detail[idx].keterangan.error }}</small>
                                </td>
                                <td class="text-center">
                                    <span
                                        class="form-control form-dark input-sm">{{form.detail[idx].qty_retur.value}}</span>
                                </td>
                                <td class="text-center">
                                    <button class="btn btn-danger btn-sm" title="hapus" @click="onDeleteDetail(idx)">
                                        <i class="fa fa-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <button class="btn btn-primary btn-sm" @click="onAddProduct()"
                        :disabled="form.inputs.outlet.value ==''">Tambah Data <i class="fa fa-plus"></i></button>
                </div>
            </div>
            <br>
            <div class="row">
                <div class="col-sm-5">
                    <textarea name="keterangan" id="keterangan"
                        :class="['col-sm-12 form-control form-dark', 'input-sm']" v-model="form.inputs.keterangan.value"
                        rows="5" placeholder="Keterangan"></textarea>
                </div>
                <div class="col-sm-6 col-offset-6 pull-right">
                    <div class="form-group row mb-1">
                        <label for="invoice" class="col-sm-5 col-form-label col-form-label-sm text-right">Sub Total
                            :</label>
                        <div class="col-sm-6 text-right">
                            <p id="sub_total" class="text-bold">{{numberFormat(subTotal)}}</p>
                        </div>
                    </div>
                    <div class="form-group row mb-1" v-if="setting.disc">
                        <label for="invoice" class="col-sm-5 col-form-label col-form-label-sm text-right">Diskon per
                            Baris :</label>
                        <div class="col-sm-6 text-right">
                            <p id="sub_total" class="text-bold">{{numberFormat(totalDiskon)}}</p>
                        </div>
                    </div>
                    <div class="form-group row mb-1" v-if="setting.disNota">
                        <label for="invoice" class="col-sm-5 col-form-label col-form-label-sm text-right">Diskon
                            :</label>
                        <div class="col-sm-6 text-right">
                            <span> {{form.inputs.discNota.value}} {{form.inputs.discNota.type=='percent'?'%':''}} ->
                                {{numberFormat(form.inputs.discNota.nominal)}}</span>
                        </div>
                        <div class="col-sm-1 text-left">
                            <a @click="onDisc()" style="cursor: pointer;color:#fe9e01"><i class="fa fa-edit"></i></a>

                        </div>
                    </div>
                    <div class="form-group row mb-1" v-if="setting.pajak" v-for="(i,idx) in totalPajak().detail">
                        <label for="invoice"
                            class="col-sm-7 col-form-label col-form-label-sm text-right">{{i.text}}</label>
                        <div class="col-sm-4 text-right">
                            <p id="payment" class="text-bold">{{numberFormat(i.nominal)}}</p>
                        </div>
                    </div>
                    <hr class="col-sm-5 col-sm-offset-6 text-right" v-if="setting.pajak">
                    <div class="form-group row mb-1" v-if="setting.pajak">
                        <label for="invoice" class="col-sm-5 col-form-label col-form-label-sm text-right">Total
                            Pajak</label>
                        <div class="col-sm-6 text-right">
                            <p id="total-pajak" class="text-bold">{{numberFormat(totalPajak().total)}}</p>
                        </div>
                    </div>
                    <div class="form-group row mb-1">
                        <label for="invoice" class="col-sm-5 col-form-label col-form-label-sm text-right">Grand Total
                            :</label>
                        <div class="col-sm-6 text-right">
                            <p id="grand-total" class="text-bold">{{numberFormat(grandTotal)}}</p>
                        </div>
                    </div>
                    <hr class="col-sm-5 col-sm-offset-6 text-right">
                    <div class="form-group row mb-1">
                        <label for="payment" class="col-sm-5 col-form-label col-form-label-sm text-right">Pembayaran
                            :</label>
                        <div class="col-sm-6 text-right">
                            <span>{{form.inputs.payment_media.value=='card'?form.inputs.payment_media_bank.text:form.inputs.payment_media.value}}
                                : {{numberFormat(form.inputs.payment.value)}}</span>
                        </div>
                        <div class="col-sm-1 text-left">
                            <a style="cursor: pointer;color:#fe9e01" data-toggle="modal" data-target="#modalPayement">
                                <i class="fa fa-edit"></i></a>

                        </div>
                    </div>
                    <div class="form-group row mb-1">
                        <label for="payment" class="col-sm-5 col-form-label col-form-label-sm text-right">Sisa Tagihan
                            :</label>
                        <div class="col-sm-6 text-right">
                            <p id="sub_total" class="text-bold">{{numberFormat(sisaTagihan().sisa)}}</p>
                        </div>
                    </div>
                    <div class="form-group row mb-1" v-if="form.inputs.tempo.required">
                        <label for="tempo" class="col-sm-5 col-form-label col-form-label-sm text-right">Jatuh Tempo
                            :</label>
                        <div class="col-sm-6 text-right">
                            <input type="date" :class="['form-control form-dark', 'input-sm']"
                                v-model="form.inputs.tempo.value" v-datepicker="form.inputs.tempo.value" placeholder=""
                                required id="tempo" />
                            <small class="invalid-feedback">{{ form.inputs.tempo.error }}</small>
                        </div>
                    </div>
                    <div class="form-group row mb-1" v-if="sisaTagihan().lebih != 0">
                        <label for="payment" class="col-sm-5 col-form-label col-form-label-sm text-right">Debit Memo
                            :</label>
                        <div class="col-sm-6 text-right">
                            <p id="sub_total" class="text-bold">{{numberFormat(sisaTagihan().lebih)}}</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="modal-footer">
                    <a href="<?= base_url() ?>purchase/purchasing_product">
                        <button type="button" class="btn btn-default">Batal</button>
                    </a>
                    <button type="button" class="btn btn-primary" @click="onSubmit()">Simpan</button>
                </div>
            </div>
        </div>
    </div>

    <!-- modal payment -->
    <div class="modal fade" id="modalPayement" tabindex="-1" role="dialog" aria-labelledby="modalPurchaseLabel"
        data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog " role="document">
            <div class="modal-content" style="background: #27292a;">
                <div class="modal-header">
                    <h4 class="modal-title" id="exampleModalp">Pembayaran Pembelian</h4>
                </div>
                <div class="modal-body">
                    <div class="form-group row mb-1">
                        <label for="invoice" class="col-sm-3 col-form-label col-form-label-sm text-right">Nominal
                            *</label>
                        <div class="col-sm-9">
                            <input type="text" id="payment"
                                :class="['form-control form-dark text-right', 'input-sm', form.inputs.payment.error ? 'is-invalid' : '']"
                                name="payment" v-model="form.inputs.payment.value" v-money="form.inputs.payment.value"
                                required />
                            <div class="invalid-feedback">{{ form.inputs.payment.error }}</div>
                        </div>
                    </div>
                    <div class="form-group row mb-1">
                        <label for="shift" class="col-sm-3 col-form-label col-form-label-sm text-right">
                            Type *
                        </label>
                        <div class="col-sm-9">
                            <select
                                :class="['form-control form-dark','select2select no-search', form.inputs.payment_media.error ? 'is-invalid' : '']"
                                name="payment_media" v-model="form.inputs.payment_media.value"
                                v-select="form.inputs.payment_media.value" style="width:100%"
                                data-placeholder="-- Tipe Pembayaran --">
                                <option value="cash">CASH</option>
                                <option value="card">CARD</option>
                            </select>
                            <div class="invalid-feedback">{{ form.inputs.payment_media.error }}</div>
                        </div>
                    </div>
                    <div class="form-group row mb-1" v-show="form.inputs.payment_media.value=='card'">
                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">
                            Bank*
                        </label>
                        <div class="col-sm-9">
                            <select id="bank"
                                :class="['form-control form-dark','select2select ', form.inputs.payment_media_bank.error ? 'is-invalid' : '']"
                                name="payment_media_bank" v-model="form.inputs.payment_media_bank.value"
                                v-select="form.inputs.payment_media_bank.value" style="width:100%"
                                data-placeholder="-- Pilih Bank --">
                                <?php foreach ($form_select_bank as $a): ?>
                                    <option value="<?= $a->bank_id ?>"><?= htmlentities($a->name) ?></option>
                                <?php endforeach ?>
                            </select>
                            <div class="invalid-feedback">{{ form.inputs.payment_media_bank.error }}</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-dismiss="modal" @click="onPay()">Simpan</button>
                </div>
            </div>
        </div>
    </div>

    <!-- modal diskon nota -->
    <div class="modal fade" id="modalDisc" tabindex="-1" role="dialog" aria-labelledby="modalDiscLabel"
        data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog " role="document">
            <div class="modal-content" style="background: #27292a;">
                <div class="modal-header">
                    <h4 class="modal-title" id="exampleModalp">Diskon Nota</h4>
                </div>
                <div class="modal-body">
                    <div class="form-group row mb-1">
                        <label for="shift" class="col-sm-3 col-form-label col-form-label-sm text-right">
                            Type *
                        </label>
                        <div class="col-sm-9">
                            <select class="form-control form-dark select2select no-search" name="discNota"
                                v-model="form.inputs.discNota.type" v-select="form.inputs.discNota.type"
                                style="width:100%" data-placeholder="-- Tipe Diskon --">
                                <option value="percent">Percent</option>
                                <option value="nominal">Nominal</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group row mb-1">
                        <label for="invoice" class="col-sm-3 col-form-label col-form-label-sm text-right">Jumlah Diskon
                            *</label>
                        <div class="col-sm-9">
                            <input type="text" id="discNota"
                                :class="['form-control form-dark text-right', 'input-sm', form.inputs.discNota.error ? 'is-invalid' : '']"
                                name="discNota" v-model="form.inputs.discNota.value"
                                v-number="form.inputs.discNota.value" required />
                            <div class="invalid-feedback">{{ form.inputs.discNota.error }}</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Batal</button>
                    <button type="button" data-dismiss="modal" class="btn btn-primary"
                        @click="onSaveDisNota()">Simpan</button>
                </div>
            </div>
        </div>
    </div>

    <!-- modal diskon items -->
    <div class="modal fade" id="modalDiscDetail" tabindex="-1" role="dialog" aria-labelledby="modalDiscLabel"
        data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog " role="document">
            <div class="modal-content" style="background: #27292a;">
                <div class="modal-header">
                    <h4 class="modal-title" id="exampleModalp">Diskon Item</h4>
                </div>
                <div class="modal-body">
                    <input type="hidden" id="idx">
                    <div class="form-group row mb-1">
                        <label for="shift" class="col-sm-3 col-form-label col-form-label-sm text-right">
                            Type *
                        </label>
                        <div class="col-sm-9">
                            <select class="form-control form-dark select2select no-search" name="discItem"
                                id="typeDisItem" style="width:100%" data-placeholder="-- Tipe Diskon --">
                                <option value="percent">Percent</option>
                                <option value="nominal">Nominal</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group row mb-1">
                        <label for="invoice" class="col-sm-3 col-form-label col-form-label-sm text-right">Diskon
                            *</label>
                        <div class="col-sm-9">
                            <input type="text" id="nominalDisItem"
                                :class="['form-control form-dark text-right', 'input-sm']" name="nominalDisItem"
                                required />
                            <div class="invalid-feedback">{{ form.inputs.discNota.error }}</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" @click="onSaveDisDetail()">Simpan</button>
                </div>
            </div>
        </div>
    </div>

    <!-- modal setting -->
    <div class="modal fade" id="modalSetting" tabindex="-1" role="dialog" aria-labelledby="modalPurchaseLabel"
        data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-sm" role="document">
            <div class="modal-content" style="background: #27292a;">
                <div class="modal-header">
                    <h4 class="modal-title" id="exampleModalp">Pengaturan Form</h4>
                </div>
                <div class="modal-body">
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" class="checkbox-setting" v-model="setting.hargaTermasukPajak"> Harga
                            Termasuk Pajak
                        </label>
                    </div>
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" class="checkbox-setting" v-model="setting.pajak"> Pajak Baris
                        </label>
                    </div>
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" class="checkbox-setting" v-model="setting.disc"> Diskon Baris
                        </label>
                    </div>
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" class="checkbox-setting" v-model="setting.keterangan"> Keterangan
                        </label>
                    </div>
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" class="checkbox-setting" v-model="setting.disNota"> Diskon Nota
                        </label>
                    </div>
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" class="checkbox-setting" v-model="setting.total"> Total
                        </label>
                    </div>
                    <!-- <div class="checkbox">
                        <label>
                            <input type="checkbox" class="checkbox-setting" v-model="setting.unitStock"> Unit Stock
                        </label>
                    </div>
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" class="checkbox-setting" v-model="setting.hargaStock"> Harga Stock
                        </label>
                    </div> -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-dismiss="modal">Simpan</button>
                </div>
            </div>
        </div>
    </div>

    <!-- modal PO -->
    <div class="modal fade" id="modalPo" tabindex="-1" role="dialog" aria-labelledby="modalPurchaseLabel"
        data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog " role="document">
            <div class="modal-content" style="background: #27292a;">
                <div class="modal-header">
                    <h4 class="modal-title" id="exampleModalp">Masukan No PO</h4>
                </div>
                <div class="modal-body">
                    <div class="form-group row mb-1">
                        <!-- <label for="invoice" class="col-sm-3 col-form-label col-form-label-sm text-right">Nominal *</label> -->
                        <div class="col-sm-12">
                            <input type="text" id="po"
                                :class="['form-control form-dark', 'input-sm', po.error ? 'is-invalid' : '']" name="po"
                                v-model="po.value" required placeholder="number of purchase order" />
                            <div class="invalid-feedback">{{ po.error }}</div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" @click="onImportPo()">Import PO</button>
                </div>
            </div>
        </div>
    </div>

</div>

<script>
    $(document).ready(function () {
        $(".checkbox-setting").on('change', function () {
            if ($(this).is(':checked')) {
                $(this).attr('value', 'true');
            } else {
                $(this).attr('value', 'false');
            }
        });

        $("#bank").on('select2:select', function (e) {
            var data = e.params.data;
            vm.form.inputs.payment_media_bank.text = data.text
        });

        // vm.form.inputs.tgl.value = moment(new Date()).format('MM-DD-YYYY')


        // $("#form-product0' option:selected").attr("data-unit_id");

        $('#tgl').datepicker({
            autoclose: true,
            format: "dd-mm-yyyy",
            // startDate: new Date(),
            todayHighlight: true,
            endDate: "today"
        });
    });

    var vm = new Vue({
        el: "#form-purchase",
        data: {
            product: [],
            po: {
                title: "Purchase Order",
                value: "",
                error: "",
                allowClear: true,
                required: true,
            },
            setting: {
                pajak: false,
                disc: false,
                qtyStock: true,
                unitStock: true,
                hargaStock: true,
                disNota: false,
                hargaTermasukPajak: false,
                total: false,
                keterangan: false
            },
            form: {
                type: "insert",
                po: false,
                inputs: {
                    purchase_id: {
                        title: "purchase id",
                        value: "<?= $data_purchase->purchase_id ?>",
                        error: "",
                        allowClear: true,
                        required: false,
                    },
                    invoice: {
                        title: "Invoice",
                        value: "<?= $data_purchase->invoice ?>",
                        error: "",
                        allowClear: true,
                        required: false,
                    },
                    keterangan: {
                        title: "Keterangan",
                        value: "<?= $data_purchase->keterangan ?>",
                        error: "",
                        allowClear: true,
                        required: false,
                    },
                    outlet: {
                        title: "Outlet",
                        value: "<?= $data_purchase->outlet_fkid ?>",
                        error: "",
                        allowClear: true,
                        required: true,
                    },
                    shift: {
                        title: "Shift",
                        value: "<?= $data_purchase->shift_fkid ?>",
                        error: "",
                        allowClear: true,
                        required: true,
                    },
                    supplier: {
                        title: "Supplier",
                        value: "<?= $data_purchase->supplier_fkid ?>",
                        error: "",
                        allowClear: true,
                        required: true,
                    },
                    tgl: {
                        title: "Tanggal",
                        value: milisToLocal("<?= $data_purchase->date_purchase ?>", "DD-MM-YYYY"),
                        error: "",
                        allowClear: true,
                        required: true,
                    },
                    sub_total: {
                        title: "Sub Total",
                        value: "<?= $data_purchase->sub_total ?>",
                        error: "",
                        allowClear: true,
                        required: false,
                    },
                    grand_total: {
                        title: "Grand Total",
                        value: "<?= $data_purchase->grand_total ?>",
                        error: "",
                        allowClear: true,
                        required: false,
                    },
                    pajak: {
                        title: "Pajak",
                        value: "<?= $data_purchase->total_pajak ?>",
                        error: "",
                        allowClear: true,
                        required: false,
                    },
                    payment: {
                        title: "Pembayaran",
                        value: "<?php echo $data_purchase->bayar + $data_purchase->debt_payment ?>",
                        error: "",
                        allowClear: true,
                        required: false,
                    },
                    payment_media: {
                        title: "Tipe Pembayaran",
                        value: "<?= $data_purchase->pay_type ?>",
                        error: "",
                        allowClear: true,
                        required: false,
                    },
                    payment_media_bank: {
                        title: "Bank",
                        value: "<?= $data_purchase->payment_media_bank_fkid ?>",
                        error: "",
                        allowClear: true,
                        text: "",
                        required: false,
                    },
                    discNota: {
                        title: "Diskon Nota",
                        value: "<?= $data_purchase->discount_total ?>",
                        error: "",
                        allowClear: true,
                        type: "percent",
                        nominal: "0",
                        required: false,
                    },
                    tempo: {
                        title: "Jatuh Tempo",
                        value: "<?= $data_purchase->jatuh_tempo ?>",
                        error: "",
                        allowClear: true,
                        required: false,
                    }
                },
                detail: []
            },
        },
        methods: {
            onAddProduct(param = null) {
                var detailForm = {
                    purchase_product_id: {
                        value: !!param ? param.purchase_products_id : "",
                    },
                    product_id: {
                        title: "Produk",
                        value: !!param ? param.products_fkid : "",
                        error: "",
                        required: true,
                        product_id: "",
                        qtyKonversi: "1",
                        product_name: ""
                    },
                    unit_nota: {
                        title: "Unit Nota",
                        value: !!param ? param.unit_fkid_nota : "",
                        error: "",
                        required: true,
                        disabled: false,
                    },
                    qty_nota: {
                        title: "Qty Nota",
                        value: !!param ? param.qty_nota.replace(".", ",") : "",
                        error: "",
                        required: true,
                    },
                    harga_nota: {
                        title: "Harga Nota",
                        value: !!param ? param.price_nota : "",
                        error: "",
                        required: true,
                    },
                    sub_total: {
                        value: !!param ? param.total : "",
                        error: "",
                        required: true,
                    },
                    disc: {
                        value: !!param ? param.discount : "",
                        error: "",
                        required: false,
                        hidden: true,
                        type: 'percent',
                        nominal: "0"
                    },
                    pajak: {
                        value: !!param ? param.tax : "",
                        error: "",
                        type: "percent",
                        nominal: "",
                        text: "",
                        required: false,
                        hidden: true
                    },
                    total: {
                        value: !!param ? param.tot_dis : "",
                        error: "",
                        required: false,
                    },
                    qty_stock: {
                        title: "Qty Stok",
                        value: !!param ? param.qty_stok.replace(".", ",") : "",
                        error: "",
                        required: true,
                        hidden: true
                    },
                    unit_stock: {
                        title: !!param ? param.unit_stock : "",
                        value: !!param ? param.unit_fkid_stok : "",
                        error: "",
                        required: true,
                        hidden: true
                    },
                    harga_stock: {
                        value: !!param ? param.price_stok : "",
                        error: "",
                        required: true,
                        hidden: true
                    },
                    diterima: {
                        value: !!param ? param.qty_arive.replace(".", ",") : "",
                        error: "",
                        required: true,
                    },
                    keterangan: {
                        value: !!param ? param.keterangan : "",
                        error: "",
                        required: false,
                    },
                    qty_retur: {
                        value: !!param ? param.qty_retur : "",
                        error: "",
                        required: false,
                    },
                }
                this.form.detail.push(detailForm)

            },
            select2Product(element) {
                $(document).ready(function () {
                    $('.' + element).select2({
                        minimumInputLength: 1,
                        language: {
                            inputTooShort: function (args) {
                                return "Input Karakter";
                            }
                        },
                        ajax: {
                            url: "<?= base_url() ?>purchasing/purchasing_product/get_product",
                            dataType: 'json',
                            type: "post",
                            delay: 250,
                            data: function (params) {
                                return {
                                    key: params.term, // search term
                                    outlet: vm.form.inputs.outlet.value
                                };
                            },
                            processResults: function (response) {
                                return {
                                    results: $.map(response, function (item) {
                                        return {
                                            text: item.product_name,
                                            id: item.product_detail_id,
                                            unit_id: item.unit_id,
                                            unit: item.unit_name,
                                            product_id: item.product_id,
                                            last_price: item.last_price,
                                        }
                                    })
                                };
                            },
                        }
                    });
                });
            },
            onPay() {
                // $('#modalPayement').modal('show')
                var sisa = this.sisaTagihan().sisa
                if (sisa > 0) {
                    this.form.inputs.tempo.required = true;
                } else {
                    this.form.inputs.tempo.required = false;
                    this.form.inputs.tempo.value = "";
                }
            },
            onSetting() {
                $('#modalSetting').modal('show')
            },
            onDeleteDetail(idx) {
                this.form.detail.splice(idx, 1);
                this.onSaveDisNota()
            },
            subTotalDetail(idx) {
                var subTotal = 0
                var priceNota = 0
                subTotal = clearFormat(this.form.detail[idx].qty_nota.value) * clearFormat(this.form.detail[idx].harga_nota.value)
                this.form.detail[idx].sub_total.value = number(subTotal)
                this.total(idx)
                this.konversi(idx)
                this.price(idx)
            },
            priceNota(idx) {
                // jika yg di ketik adalah sub totalnya
                priceNota = clearFormat(this.form.detail[idx].sub_total.value) / clearFormat(this.form.detail[idx].qty_nota.value)
                this.form.detail[idx].harga_nota.value = number(priceNota)
                this.total(idx)
            },
            setUnitStock(idx) {
                $(function () {
                    $('.form-product' + idx).on('select2:select', function (e) {
                        var data = e.params.data;
                        vm.form.detail[idx].unit_stock.title = data.unit
                        vm.form.detail[idx].unit_stock.value = data.unit_id
                        vm.form.detail[idx].product_id.product_id = data.product_id
                        vm.form.detail[idx].harga_nota.value = number(data.last_price)
                        vm.form.detail[idx].unit_nota.disabled = false
                        vm.form.detail[idx].qty_nota.value = number(1)
                        vm.form.detail[idx].diterima.value = number(1)
                        vm.form.detail[idx].product_id.product_name = data.text
                        vm.subTotalDetail(idx)
                    });
                });
            },
            subTotalPajak(idx) { //jika harga nota termasuk pajak di centang
                var subTotal = 0
                if (this.setting.hargaTermasukPajak == true) {
                    if (this.form.detail[idx].pajak.value != "") {
                        subTotal = this.price(idx) * clearFormat(this.form.detail[idx].qty_nota.value)
                    } else {
                        subTotal = clearFormat(this.form.detail[idx].sub_total.value)
                    }
                } else {
                    subTotal = clearFormat(this.form.detail[idx].sub_total.value)
                }

                return subTotal
            },
            total(idx) {
                var total = 0
                var totalDisc = 0
                var typeDisc = this.form.detail[idx].disc.type
                var disc = clearFormat(this.form.detail[idx].disc.value)
                var subTotal = this.subTotalPajak(idx)

                // mencari diskon
                if (typeDisc == "percent") {
                    totalDisc = subTotal * disc / 100
                } else {
                    totalDisc = disc
                }
                this.form.detail[idx].disc.nominal = totalDisc
                total = subTotal - totalDisc
                this.form.detail[idx].total.value = number(total)
                this.hargaStock(idx)
                this.onSaveDisNota()
                this.onPay()
            },
            onChangeUnit(idx) {
                $.ajax({
                    type: "get",
                    url: "<?= base_url() ?>purchasing/purchasing_product/conversion",
                    data: {
                        product_id: vm.form.detail[idx].product_id.product_id,
                        unit_id: vm.form.detail[idx].unit_nota.value,
                    },
                    beforeSend() {
                        $("#table-purchase").notify(
                            "Mengkonversi Kedalam Satuan Stok ...",
                            {
                                arrowShow: false,
                                className: 'info',
                                position: "top center"
                            }
                        );
                    },
                    success: function (res) {
                        var text = ""
                        var data = $('.form-product' + idx).select2('data');
                        var unitNota = $('.unit' + idx).select2('data');
                        var qtyStock = 0
                        // console.log(res.length);
                        if (res) {
                            text = "Merubah 1 " + unitNota[0].text + " " + vm.form.detail[idx].product_id.product_name + " menjadi" + res.qty + " " + vm.form.detail[idx].unit_stock.title
                            vm.form.detail[idx].qty_stock.value = clearFormat(vm.form.detail[idx].qty_nota.value) * res.qty
                            vm.form.detail[idx].product_id.qtyKonversi = res.qty
                        } else {
                            text = "Merubah 1" + unitNota[0].text + " " + vm.form.detail[idx].product_id.product_name + " menjadi 1 " + vm.form.detail[idx].unit_stock.title
                            vm.form.detail[idx].qty_stock.value = number(vm.form.detail[idx].qty_nota.value)
                            vm.form.detail[idx].product_id.qtyKonversi = number(1)
                        }

                        vm.total(idx)

                        $("#table-purchase").notify(
                            text,
                            {
                                arrowShow: false,
                                className: 'success',
                                position: "top center",
                                showAnimation: 'slideDown',
                                showDuration: 400,
                                hideAnimation: 'slideUp',
                                hideDuration: 200,
                                autoHideDelay: 8000,
                            }
                        );
                    },
                });
            },
            konversi(idx) {
                var qtyStock = 0
                qtyStock = clearFormat(this.form.detail[idx].qty_nota.value) * clearFormat(this.form.detail[idx].product_id.qtyKonversi)
                this.form.detail[idx].qty_stock.value = number(qtyStock)
                if (clearFormat(this.form.detail[idx].diterima.value) == "") {
                    this.form.detail[idx].diterima.value = number(qtyStock)
                }

                this.total(idx)
            },
            onDisc() {
                $("#modalDisc").modal("show")
                $("#nominalDisItem").val("")
                $("#typeDisItem").val("percent")
            },
            onSaveDisNota() {
                // $("#modalDisc").modal("hide")
                var disNominal = 0
                if (this.form.inputs.discNota.type == "percent") {
                    disNominal = (this.subTotal - this.totalDiskon) * clearFormat(this.form.inputs.discNota.value) / 100
                } else {
                    disNominal = clearFormat(this.form.inputs.discNota.value)
                }
                this.form.inputs.discNota.nominal = disNominal

                // diskon di bagi per produk
                var total = 0
                var subDiskon = 0
                for (let i in this.form.detail) {
                    var disc = this.discPerProduct(i)
                    subDiskon = this.subTotalPajak(i) - this.form.detail[i].disc.nominal
                    total = subDiskon - disc
                    this.form.detail[i].total.value = number(total)
                    this.hargaStock(i)
                }
            },
            onDiscDetail(idx, type, nominal) {
                $("#modalDiscDetail").modal("show")
                $("#typeDisItem").val(type)
                $("#nominalDisItem").val(nominal)
                $("#idx").val(idx)
            },
            onSaveDisDetail() {
                $("#modalDiscDetail").modal("hide")
                var idx = $("#idx").val()
                var nominal = $("#nominalDisItem").val()
                this.form.detail[idx].disc.value = nominal
                this.form.detail[idx].disc.type = $("#typeDisItem").val()
                this.total(idx)
            },
            hargaStock(idx) {
                var hargaStock = 0
                hargaStock = clearFormat(this.form.detail[idx].total.value) / clearFormat(this.form.detail[idx].qty_stock.value)
                this.form.detail[idx].harga_stock.value = number(hargaStock)
            },
            pajakDetail(idx) {
                $(function () {
                    var type = $('.pajak' + idx).select2({
                        allowClear: true,
                        placeholder: '-- Pajak --',
                    }).find(":selected").data("type");
                    var nominal = $('.pajak' + idx).select2().find(":selected").data("nominal");
                    var text = $('.pajak' + idx).select2({
                        allowClear: true,
                        placeholder: '-- Pajak --',
                    }).find(":selected").text();
                    // console.log(type);
                    if (!type) {
                        vm.form.detail[idx].pajak.type = 'percent'
                        vm.form.detail[idx].pajak.nominal = 0
                        vm.form.detail[idx].pajak.text = ""
                        vm.form.detail[idx].pajak.value = ""
                    } else {
                        vm.form.detail[idx].pajak.type = type
                        vm.form.detail[idx].pajak.nominal = nominal
                        vm.form.detail[idx].pajak.text = text
                    }

                    vm.total(idx)
                });
            },
            numberFormat(val) {
                return number(val)
            },
            discPerProduct(idx) { //pembagian diskon per produk
                //sub total murni produk/sub total murni nota * nominal diskon nota
                var subtToal = 0;
                var subDisc = 0
                subDisc = clearFormat(this.form.detail[idx].sub_total.value) - this.form.detail[idx].disc.nominal
                subtToal = this.subTotal - this.totalDiskon
                // console.log("total diskon:"+this.totalDiskon);
                var disProduct = (subDisc / subtToal) * this.form.inputs.discNota.nominal
                return disProduct
            },
            onSubmit() {
                //validasi form
                if (!this.validate(this.form.inputs)) {
                    return;
                }
                // validasi detail
                for (const i in this.form.detail) {
                    if (!this.validateDetail(this.form.detail[i])) {
                        Alert.warning("", "Periksa Kembali Inputan Anda")
                        return;
                    }
                    if (!this.validateArive(i)) {
                        Alert.warning("", "Periksa Kembali Inputan Anda")
                        return;
                    }

                }

                // console.log("validasi berhasil");
                // maping data
                var data = {};
                for (var key in this.form.inputs) {
                    if (this.form.inputs.hasOwnProperty(key)) {
                        this.form.inputs[key].error = "";
                        data[key] = this.form.inputs[key].value;
                    }
                }
                // maping data detail
                var dataDetail = []

                for (var key in this.form.detail) {
                    var tmp = {}
                    for (var i in this.form.detail[key]) {
                        this.form.detail[key][i].error = "";
                        tmp[i] = clearFormat(this.form.detail[key][i].value);
                    }
                    
                    tmp['qty_stock'] = this.form.detail[key].qty_stock.value.replace(",", ".")
                    tmp['qty_nota'] = this.form.detail[key].qty_nota.value.replace(",", ".")
                    tmp['diterima'] = this.form.detail[key].diterima.value.replace(",", ".")
                    tmp['tax_type'] = this.form.detail[key].pajak.type
                    tmp['tax_nominal'] = this.form.detail[key].pajak.nominal
                    tmp['dis_type'] = this.form.detail[key].disc.type
                    dataDetail.push(tmp)
                }
                data['payment'] = clearFormat(this.form.inputs.payment.value) - "<?= $data_purchase->debt_payment ?>"
                data['detail'] = dataDetail
                data['status_lunas'] = (this.sisaTagihan().sisa > 0) ? "hutang" : "lunas";
                data['hutang'] = this.sisaTagihan().sisa
                data['type_disc'] = this.form.inputs.discNota.type
                data['pay_type'] = this.form.inputs.payment_media.value
                data['tgl'] = new Date(reverseDate(this.form.inputs.tgl.value)).getTime();
                data['sisa_pembayaran'] = this.sisaTagihan().lebih
                data['lebih_pembayaran'] = this.sisaTagihan().sisa

                $.ajax({
                    type: "POST",
                    url: "<?= base_url('purchasing/purchasing_product/update') ?>",
                    data: data,
                    dataType: "json",
                    beforeSend() {
                        loading.show()
                    },
                    success: function (response) {
                        loading.hide()
                        Alert.success("Success", "Data Berhasil Disimpan")
                        location.href = "<?= base_url() ?>purchase/purchasing_product"
                    },
                    error(err) {
                        loading.hide();
                        console.log(err);
                        Alert.error("error", "Data Gagal Disimpan")
                    }
                });
            },
            validateDetail(inputs) {
                var label;
                var is_valid = true;
                var i = 0;
                for (var key in inputs) {
                    if (inputs.hasOwnProperty(key)) {
                        inputs[key].error = "";
                        label = inputs[key].label
                            ? inputs[key].label
                            : capitalize(key.replace(/_/g, " "));
                        if (
                            inputs[key].required &&
                            (!inputs[key].value || inputs[key].value == "0")
                        ) {
                            inputs[key].error = "Isian " + label + " harus diisi";
                            is_valid = false;
                            if (i == 0) {
                                setTimeout(function () { $('#qty-nota' + key).focus(); }, 500);
                                // document.getElementById("#qty-nota"+key).focus();
                            }
                            i++;
                        }
                    }
                }
                return is_valid;
            },
            price(idx) {
                var price = 0;
                if (this.setting.hargaTermasukPajak == true) {
                    if (this.form.detail[idx].pajak.value != "") {
                        if (this.form.detail[idx].pajak.type == "percentage") {
                            price = clearFormat(this.form.detail[idx].harga_nota.value) / (((100 + this.form.detail[idx].pajak.nominal) / 100))
                        }
                    }
                } else {
                    price = clearFormat(this.form.detail[idx].harga_nota.value)
                }
                // console.log(price);
                return price
            },
            // sub_total(){
            //     var subTotal = 0
            //     for (let i in this.form.detail) {
            //         subTotal += this.subTotalPajak(i)
            //     }
            //     this.form.inputs.sub_total.value = subTotal
            //     return subTotal
            // },
            totalPajak() {
                var pajakNominal = 0
                var totalPajak = 0
                var pajakArr = []
                var diskonProduct = 0
                for (let i in this.form.detail) {
                    if (this.form.detail[i].pajak.value != "") {
                        if (this.form.detail[i].pajak.type == "percentage") {
                            var disNota = 0
                            disNota = this.subTotalPajak(i) - this.form.detail[i].disc.nominal - this.discPerProduct(i)

                            pajakNominal = this.form.detail[i].pajak.nominal * disNota / 100
                        } else {
                            pajakNominal = this.form.detail[i].pajak.nominal
                        }
                        totalPajak += pajakNominal
                        var tmpArr = {
                            text: this.form.detail[i].pajak.text,
                            nominal: pajakNominal
                        }
                        pajakArr.push(tmpArr)
                    }
                    // console.log(this.form.detail[i].total.value);
                }
                // console.log(pajakArr);

                var newArr = [];
                pajakArr.reduce(function (res, value) {
                    if (!res[value.text]) {
                        res[value.text] = { text: value.text, nominal: 0 };
                        newArr.push(res[value.text])
                    }
                    res[value.text].nominal += parseInt(value.nominal);
                    return res;
                }, {});

                // this.form.inputs.pajak.value = totalPajak
                var result = {
                    total: totalPajak,
                    detail: newArr
                }
                return result
            },
            // grandTotal(){
            //     var grandTotal = 0
            //     grandTotal = this.sub_total()-this.totalDiskon-this.form.inputs.discNota.nominal+this.totalPajak().total

            //     this.form.inputs.grand_total.value = grandTotal

            //     return grandTotal

            // },
            sisaTagihan() {
                var sisa = 0
                var lebih = 0
                sisa = this.grandTotal - clearFormat(this.form.inputs.payment.value)
                if (sisa < 0) {
                    sisa = 0
                    lebih = (this.grandTotal - clearFormat(this.form.inputs.payment.value)) * -1
                }
                var result = {
                    sisa: sisa,
                    lebih: lebih
                }
                return result
            },
            validateArive(idx) {
                // console.log(this.form.detail[idx]);
                var diterima = clearFormat(this.form.detail[idx].diterima.value)
                var qty_stock = clearFormat(this.form.detail[idx].qty_stock.value)
                var qty_retur = clearFormat(this.form.detail[idx].qty_retur.value)

                switch (true) {
                    case (diterima > qty_stock):
                        this.form.detail[idx].diterima.error = "Melebihi Qty Nota"
                        return false
                        break;
                    case (diterima < qty_retur):
                        this.form.detail[idx].diterima.error = "Qty lebih rendah dari Retur"
                        return false
                        break;
                    default:
                        this.form.detail[idx].diterima.error = ""
                        return true
                        break;
                }

            },
            onPo() {
                this.po.value = ''
                this.po.error = ''
                $('#modalPo').modal('show')
            },
            onImportPo() {
                if (this.po == '') {
                    return this.po.error = 'No PO harus diisi'
                }
                $.ajax({
                    type: "post",
                    url: "<?= base_url() ?>purchase_order/purchase_order/po_for_purchase",
                    data: { id: vm.po.value },
                    dataType: "json",
                    beforeSend() {
                        loading.show();
                    },
                    success: function (res) {
                        loading.hide();
                        vm.form.detail = []
                        if (res.po_id == 0) {
                            return Alert.warning('Data Tidak Ditemukan', 'Periksa kembali nomor PO dan pastikan status PO sudah divalidasi')
                        }
                        $('#modalPo').modal('hide')
                        console.log(res);
                        vm.form.inputs.outlet.value = res.po_outlet
                        vm.form.po = true

                        for (const i in res.detail) {
                            vm.onAddProduct()

                            // $('#form-product0').select2('data', {id: 100, a_key: 'Lorem Ipsum'});

                            console.log(i);
                            vm.form.detail[i].unit_stock.title = res.detail[i].unit
                            vm.form.detail[i].unit_stock.value = res.detail[i].unit_fkid
                            vm.form.detail[i].product_id.product_id = res.detail[i].product_id
                            vm.form.detail[i].product_id.value = res.detail[i].product_detail_fkid
                            vm.form.detail[i].harga_nota.value = number(res.detail[i].last_price)
                            vm.form.detail[i].unit_nota.disabled = false
                            vm.form.detail[i].qty_nota.value = number(res.detail[i].qty)
                            vm.form.detail[i].diterima.value = number(res.detail[i].qty)
                            vm.form.detail[i].product_id.product_name = res.detail[i].product_name
                            setTimeout(() => {
                                vm.subTotalDetail(i)
                                $(document).ready(function () {
                                    $('.unit' + i).select2({
                                        allowClear: true,
                                        placeholder: '-- Pilih Unit -- ',
                                    });
                                    $('#tax-id' + i).select2({
                                        allowClear: true,
                                        placeholder: '-- Pajak --',
                                    });

                                })
                            }, 200);
                        }
                        setTimeout(() => {
                            $("#outlet").val(res.po_outlet);
                            $("#outlet").trigger('change');
                        }, 200);

                    },
                    error(err) {
                        loading.hide();
                        Alert.error('error', 'Gagal membuat data')
                    }
                });
            },
            getDetail() {
                $.ajax({
                    type: "post",
                    url: "<?= base_url('purchasing/purchasing_product/detail') ?>",
                    data: { id: "<?= $purchase_id ?>" },
                    beforeSend() {
                        loading.show()
                    },
                    success: function (res) {
                        loading.hide();
                        vm.form.type = "update"
                        res.detail.map((num, idx) => {
                            vm.onAddProduct(num)
                            setTimeout(() => {
                                // $("#product_id"+idx).select2("val",num.products_fkid);
                                $("#product_id" + idx).select2();
                                $("#product_id" + idx).val(num.products_fkid).trigger('change');

                                $(".unit" + idx).select2();
                                $(".unit" + idx).val(num.unit_fkid_nota).trigger('change');

                                $(".tax-id" + idx).select2();
                                $(".tax-id" + idx).val(num.tax).trigger('change');
                            }, 300);
                        })
                    },
                    error(err) {
                        Alert.error("error", "Gagal menampilkan data")
                        loading.hide();
                        console.log(err);
                    }
                });
            },
            getMasterProduct() {
                $.ajax({
                    type: "post",
                    url: "<?= base_url('purchasing/purchasing_product/get_detail_product_edit') ?>",
                    data: { outlet_id: "<?= $data_purchase->outlet_fkid ?>" },
                    beforeSend() {
                        loading.show()
                    },
                    success: function (res) {
                        vm.product = res;
                        vm.getDetail();
                    },
                    error(err) {
                        loading.hide()
                        Alert.error("error", "Gagal menampilkan data produk")
                        console.log(err);
                    }
                });
            }

        },
        watch: {
            'form.detail'(newVal) {
                var totalArray = this.form.detail.length - 1

                // console.log(totalArray);
                this.select2Product('form-product' + totalArray)
                $(document).ready(function () {
                    $('.unit' + totalArray).select2({
                        allowClear: true,
                        placeholder: '-- Pilih Unit -- ',
                    });
                    $('#tax-id' + totalArray).select2({
                        allowClear: true,
                        placeholder: '-- Pajak --',
                    });

                })

                // disable selct outlet
                if (this.form.detail.length > 1) {
                    $('#outlet').prop('disabled', true);
                } else {
                    $('#outlet').prop('disabled', false);
                }
                // this.sub_total()
                this.totalPajak()
                this.sisaTagihan()
            },
            'form.inputs.outlet.value'(newVal) {
                if (this.form.detail.length == 0) {
                    this.onAddProduct()
                }

            },
            'setting.hargaTermasukPajak'(newVal) {
                // console.log(newVal);
                var price = 0;
                for (var i in this.form.detail) {
                    this.subTotalDetail(i)
                }
            },
            'setting.pajak'(newVal) {
                if (newVal == false) {
                    Alert.confirm("Anda Yakin Akan Menghapus Semua Pajak?", "Semua inputan pajak akan dihapus").then(function (result) {
                        if (!result.value) {
                            vm.setting.pajak = true
                            return;
                        } else if (result.value) {
                            for (var i in vm.form.detail) {
                                vm.form.detail[i].pajak.value = ""
                                vm.form.detail[i].pajak.type = "percent"
                                vm.form.detail[i].pajak.nominal = "0"
                                vm.total(i)
                            }
                        }


                    });
                }
            },
            'setting.disc'(newVal) {
                if (newVal == false) {
                    Alert.confirm("Anda Yakin Akan Menghapus Semua Diskon?", "Semua inputan diskon baris akan dihapus").then(function (result) {
                        if (!result.value) {
                            vm.setting.disc = true
                            return;
                        } else if (result.value) {
                            for (var i in vm.form.detail) {
                                vm.form.detail[i].disc.value = "0"
                                vm.form.detail[i].disc.type = "percent"
                                vm.form.detail[i].disc.nominal = "0"
                                vm.total(i)
                            }
                        }


                    });
                }
            }

        },
        computed: {
            'totalDiskon'() {
                var totalDisc = 0
                for (let i in this.form.detail) {
                    if (this.form.detail[i].disc.nominal != "0") {
                        totalDisc += this.form.detail[i].disc.nominal
                    }

                }
                return totalDisc
            },
            'subTotal'() {
                var subTotal = 0
                for (let i in this.form.detail) {
                    subTotal += this.subTotalPajak(i)
                }
                this.form.inputs.sub_total.value = subTotal
                return subTotal
            },
            'grandTotal'() {
                var grandTotal = 0
                grandTotal = this.subTotal - this.totalDiskon - this.form.inputs.discNota.nominal + parseFloat(this.totalPajak().total)
                this.form.inputs.grand_total.value = grandTotal

                return grandTotal
            }
        },
        mounted() {
            this.getMasterProduct();
        },

    })


</script>