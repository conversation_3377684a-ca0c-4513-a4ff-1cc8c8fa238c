<style>
    .panel-default {
        border-color: #ff9d0357;
    }

    .panel {
        background-color: #27292a;
    }
    div.dataTables_scrollBody {
        background: #27292a !important;
    }
    .tab-uniq > li.active > a, .tab-uniq > li.active > a:hover, .tab-uniq > li.active > a:focus {
        color: #F2C94C;
        cursor: default;
        border: 1px solid #6aa50d;
        border-bottom-color: transparent;
    }
    .active {
        color: #fff;
    }
     .select2-search { background-color: #2d2d2d; }
    .select2-search input { background-color: #2d2d2d; }
    .select2-results { background-color: #2d2d2d }
    /* .select2-choice { background-color: #2d2d2d !important; } */
    .select2-container--default .select2-selection--single .select2-selection__rendered { color: #fff; }
    .select2-container--default .select2-selection--single{
        background-color: #2d2d2d;
        color: #fff;
        border-color: #6c757d;
    }
    .select2-container--default .select2-results__option[aria-selected=true] {
        background-color: #424242;
    }
   

</style>
<div id="app">	
	<div class="container-fluid">
	    <div class="content-uniq">
            <div class="row">
                <div class="col-sm-4">
                    <span><i class="fa fa-gears"></i> Pembelian</span>
                    <a class="btn btn-sm btn-primary" href="<?=base_url()?>purchase/purchasing_product/form_purchase">Tambah <i class="fa fa-plus"></i></a>
                </div>
                <div class="col-sm-8"> 
                    <div class="pull-right">
                        <div class="btn-group pull-right">
                            <button class="btn btn-primary btn-block btn-apply" type="button" @click="onApply">Apply</button>                               
                        </div>
                        <div class="btn-group pull-right">
                            <select class="form-control btn btn-info outletSelect" v-model="form.outlet" v-select="form.outlet" id="outlet-id" multiple>
                                <?php foreach ($form_select_outlet as $a): ?>                   
                                    <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
                                <?php endforeach ?>                
                            </select>                                   
                        </div>
                        <div class="btn-group pull-right">
                            <button type="button" class="btn btn-info daterange" id="date-purchase" >
                                <span> 
                                    <i class="glyphicon glyphicon-calendar"></i> Date
                                </span>
                                <i class="caret"></i>
                            </button>
                        </div>
                    </div>             
                </div> 
                
                <div class="col-sm-8">
                    <div class="btn-group pull-right">
		                
		            </div>
                </div>
            </div>
            <hr>
	    	<div class="row">
                <div class="col-sm-12 table-responsive">
	    			<table class="table table-condensed table-report " id="table-purchase" width="100%">
	    				<thead>
	    					<tr>
	    						<th>No</th>
	    						<th>Tanggal</th>
	    						<th>Invoice</th>
	    						<th>Outlet</th>
	    						<th>Suppier</th>
	    						<th>Grand Total</th>
	    						<!-- <th>Uang Muka</th>
	    						<th>Debt Pay</th> -->
	    						<th>Total Bayar</th>
	    						<th>Retur</th>
	    						<th>Hutang</th>
	    						<th>Piutang</th>
	    						<th>Status</th>
	    						<th>Penerimaan</th>
	    						<th>#</th>
	    					</tr>
	    				</thead>
	    			</table>			
	    		</div>
            </div>
        </div>
    </div>

    <!-- modal detail purchasing -->
    <div class="modal fade" id="modalDetail" tabindex="-1" role="dialog" aria-labelledby="modalDetail" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog " role="document" style="width: 95%">
            <div class="modal-content" style="background: #27292a;">
                <div class="modal-header">
                    <h4 class="modal-title" id="exampleModalp">Detail Pembelian</h4>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-6">
                            <div class="form-group row mb-1">
                                <label class="col-sm-3 col-form-label col-form-label-sm text-right">Invoice</label>
                                <div class="col-sm-8">
                                    <span class="form-control form-dark input-line input-sm">{{detail.invoice}}</span>
                                </div>
                            </div>
                            <div class="form-group row mb-1">
                                <label class="col-sm-3 col-form-label col-form-label-sm text-right">Outlet</label>
                                <div class="col-sm-8">
                                    <span class="form-control form-dark input-line input-sm">{{detail.outlet_name}}</span>
                                </div>
                            </div>
                            <div class="form-group row mb-1">
                                <label class="col-sm-3 col-form-label col-form-label-sm text-right">Shift</label>
                                <div class="col-sm-8">
                                    <span class="form-control form-dark input-line input-sm">{{detail.shift_name}}</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <div class="form-group row mb-1">
                                <label class="col-sm-3 col-form-label col-form-label-sm text-right">Supplier</label>
                                <div class="col-sm-8">
                                    <span class="form-control form-dark input-line input-sm">{{detail.supplier_name}}</span>
                                </div>
                            </div>
                            <div class="form-group row mb-1">
                                <label class="col-sm-3 col-form-label col-form-label-sm text-right">Tanggal</label>
                                <div class="col-sm-8">
                                    <span class="form-control form-dark input-line input-sm">{{detail.data_created}}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <br>
                    <div class="row">
                        <div class="col-sm-12">
                            <ul class="nav nav-tabs tab-uniq">
                                <li id="tabPurchase" class="active"><a style="color: #fff;padding-top:5px;padding-bottom:5px" data-toggle="tab" href="#purchase">Detail Pembelian <i class="fa fa-shopping-cart"></i></a> </li>
                                <!-- <li id="tabRetur"><a style="color: #fff;" data-toggle="tab" href="#menu1">Detail Pengembalian <i class="fa fa-undo"></i></a> </li> -->
                            </ul>
                        </div>
                    </div>
                    <div class="tab-content row">
                        <div id="purchase" class="tab-pane fade in active">
                            <div class="row container-fluid">
                                <div class="col-sm-12 table-responsive">
                                    <button class="btn btn-sm btn-primary pull-right">Export <i class="fa fa-file-export"></i></button>
                                    <table class="table table-responsive table-report" id="ttble" style="font-size: 12px;">
                                        <thead style="background: black">
                                            <tr>
                                                <th rowspan="2" style="text-align:center;vertical-align: inherit;" width="3%">No</th>
                                                <th rowspan="2" style="text-align:center;vertical-align: inherit;">Nama Barang</th>
                                                <th colspan="3" class="text-center">Nota</th>
                                                <th rowspan="2" style="text-align:center;vertical-align: inherit;">Sub Total</th>
                                                <th rowspan="2" style="text-align:center;vertical-align: inherit;">Diskon</th>
                                                <th rowspan="2" style="text-align:center;vertical-align: inherit;">Pajak</th>
                                                <th rowspan="2" style="text-align:center;vertical-align: inherit;">Total</th>
                                                <th colspan="3" class="text-center">Stok</th>
                                                <th rowspan="2" style="text-align:center;vertical-align: inherit;">Diterima</th>
                                                <th rowspan="2" style="text-align:center;vertical-align: inherit;">Retur</th>
                                                <th rowspan="2" style="text-align:center;vertical-align: inherit;">Keterangan</th>
                                            </tr>
                                            <tr>
                                                <th style="text-align:center;">Qty</th>
                                                <th style="text-align:center;">Unit</th>	
                                                <th style="text-align:center;">Harga</th>
                                                <th style="text-align:center;">Qty</th>
                                                <th style="text-align:center;">Unit</th>
                                                <th style="text-align:center;">Harga</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="(i,idx) in detail.detailPrc">
                                                <td style="text-align:center;">{{idx+1}}</td>
                                                <td>{{i.product_name}}</td>
                                                <td class="text-right">{{numberFormat(i.qty_nota)}}</td>
                                                <td>{{i.unit_nota}}</td>
                                                <td class="text-right">{{currency(i.price_nota)}}</td>
                                                <td class="text-right">{{currency(i.total)}}</td>
                                                <td class="text-right">{{i.discount}}</td>
                                                <td>{{i.tax_name}}</td>
                                                <td class="text-right">{{currency(i.tot_dis)}}</td>
                                                <td class="text-right">{{numberFormat(i.qty_stok)}}</td>
                                                <td>{{i.unit_stock}}</td>
                                                <td class="text-right">{{currency(i.price_stok)}}</td>
                                                <td class="text-right">{{numberFormat(i.qty_arive)}}</td>
                                                <td class="text-right">{{numberFormat(i.qty_retur)}}</td>
                                                <td>{{i.keterangan}}</td>
                                            </tr>
                                        </tbody>
                                    </table>                            
                                </div>
                            </div>
                            <div class="row container-fluid">
                                <div class="col-sm-5 col-sm-offset-7">
                                    <div class="form-group row mb-1">
                                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">Sub Total</label>
                                        <div class="col-sm-9">
                                            <span class="form-control form-dark input-line input-sm text-right">{{currency(detail.sub_total)}}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row mb-1" v-if="detail.diskonBaris>0">
                                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">Diskon Baris</label>
                                        <div class="col-sm-9">
                                            <span class="form-control form-dark input-line input-sm text-right">{{currency(detail.diskonBaris)}}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row mb-1" v-if="detail.disNominal>0">
                                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">Diskon Nota</label>
                                        <div class="col-sm-9">
                                            <span class="form-control form-dark input-line input-sm text-right">{{currency(detail.disNominal)}}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row mb-1" >
                                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">Pajak</label>
                                        <div class="col-sm-9">
                                            <span v-for="i in detail.pajakBaris"  class="form-control form-dark input-line input-sm text-right">{{i.text}} - {{currency(i.nominal)}}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row mb-1">
                                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">Grand Total</label>
                                        <div class="col-sm-9">
                                            <span class="form-control form-dark input-line input-sm text-right">{{currency(detail.grand_total)}}</span>
                                        </div>
                                    </div>
                                    <div class="form-group row mb-1">
                                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">Pembayaran</label>
                                        <div class="col-sm-9">
                                            <span class="form-control form-dark input-line input-sm text-right">{{currency(detail.bayar)}}</span>
                                        </div>
                                    </div>
                                    <template v-if="detail.hutang > 0">
                                        <div class="form-group row mb-1">
                                            <label class="col-sm-3 col-form-label col-form-label-sm text-right">Hutang</label>
                                            <div class="col-sm-9">
                                                <span class="form-control form-dark input-line input-sm text-right">{{currency(detail.hutang)}}</span>
                                            </div>
                                        </div>
                                        <div class="form-group row mb-1">
                                            <label class="col-sm-3 col-form-label col-form-label-sm text-right">Tempo</label>
                                            <div class="col-sm-9">
                                                <span class="form-control form-dark input-line input-sm text-right">{{reverseDate(detail.jatuh_tempo)}}</span>
                                            </div>
                                        </div>
                                    </template>
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal" id="close_form">Close</button>
                    </div>
                    <div style="clear: both;"></div>
                </div><!-- /.modal-body -->
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>
    <!-- modal end -->

    <!-- modal penerimaan pengembalian -->
    <div class="modal fade" id="modalPiutang" tabindex="-1" role="dialog" aria-labelledby="modalPiutang" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content" style="background: #27292a;">
                <div class="modal-header">
                    <h4 class="modal-title" id="exampleModalp">Penerimaan Piutang</h4>
                </div>
                <div class="modal-body">
                    <div class="form-group row mb-1">
                        <label class="col-sm-3 col-form-label col-form-label-sm text-right">
                            Total Piutang *
                        </label>
                        <div class="col-sm-9">
                            <h4>{{currency(formPiutang.data.totalSaldo)}}</h4>
                        </div>
                    </div>
                    <div class="form-group row mb-1">
                        <label for="nominal" class="col-sm-3 col-form-label col-form-label-sm text-right">Nominal *</label>
                        <div class="col-sm-9">
                            <input type="text" id="nominal" :class="['form-control form-dark text-right', 'input-sm', formPiutang.inputs.nominal.error ? 'is-invalid' : '']" name="payment" v-model="formPiutang.inputs.nominal.value" v-money="formPiutang.inputs.nominal.value" required />
                            <div class="invalid-feedback">{{ formPiutang.inputs.nominal.error }}</div>
                        </div>
                    </div>
                    <div class="form-group row mb-1">
                        <label for="bank" class="col-sm-3 col-form-label col-form-label-sm text-right"></label>
                        <div class="col-sm-9">
                            <select id="bank" :class="['form-control form-dark select2select']" name="payment_media_bank" v-model="formPiutang.inputs.bank.value" v-select="formPiutang.inputs.bank.value" style="width:100%" data-placeholder="-- Pilih Bank --">
                                <option value="cash" selected>Cash</option>
                                <?php foreach ($form_select_bank as $a) : ?>
                                    <option value="<?= $a->bank_id ?>"><?= htmlentities($a->name) ?></option>
                                <?php endforeach ?>
                            </select>
                        </div>
                    </div>
                </div><!-- /.modal-body -->
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Batal</button>
                    <button type="button" class="btn btn-primary" @click="insertPiutang()">Simpan</button>
                </div>
            </div><!-- /.modal-content -->
        </div><!-- /.modal-dialog -->
    </div>
    <!-- modal end -->
</div>


<script>
    $(function () {

        //call showPurchaseDetail if receive param url: id=10349&source=stockcard
        function checkParams(){
            var url_params = new URLSearchParams(window.location.search);
            var id = url_params.get('id');
            var source = url_params.get('source');
            if(id != null){
                purchaseApp.showPurchaseDetail(id);
            }
        }       

        $('#date-purchase').on('apply.daterangepicker', function(ev, val) {
            $(this).children('span').html(val.startDate.format("D MMMM YYYY")+' - '+val.endDate.format("D MMMM YYYY"));
            purchaseApp.form.startDate = val.startDate.valueOf()
            purchaseApp.form.endDate = val.endDate.valueOf()
        });

        $("#table-purchase").on('click', '.btn-detail', function(e) {              
            var data = $("#table-purchase").DataTable().row($(this).closest('tr')).data();
            purchaseApp.detail=data;
            console.log(data);
            purchaseApp.showPurchaseDetail(data.purchase_id);            
        })

         $("#table-purchase").on('click', '.btn-piutang', function(e) {  
            var data = $("#table-purchase").DataTable().row($(this).closest('tr')).data();
            // console.log(data);
            $('#modalPiutang').modal('show')
            var saldo = data.total_retur-data.hutang-data.refund
            purchaseApp.formPiutang.data.totalSaldo = saldo
            purchaseApp.formPiutang.inputs.purchase_id.value = data.purchase_id
            purchaseApp.formPiutang.inputs.nominal.value = 0
            purchaseApp.formPiutang.inputs.bank.value = 'cash'
            setTimeout(() => {
                $("#bank").select2();
            }, 200);

         })

         
        $("#table-purchase").on('click', '.btn-edit', function(e) {              
            var data = $("#table-purchase").DataTable().row($(this).closest('tr')).data();
            let urlEdit = '<?=base_url("purchase/purchasing_product/edit/")?>'+data.purchase_id
            window.location.replace(urlEdit)        
        })   

        $("#table-purchase").on('click', '.btn-del', function(e) {              
            var data = $("#table-purchase").DataTable().row($(this).closest('tr')).data();
            Alert.confirm().then(function(result) {
                if (!result.value) {
                    return;
                }

                $.ajax({
                    type: "post",
                    url: "<?=base_url('purchase/purchasing_product/delete')?>",
                    data: {purchase_id:data.purchase_id},
                    dataType: "json",
                    beforeSend(){
                        loading.show()
                    },
                    success(response) {
                        purchaseApp.datatable();
                        loading.hide();
                        Alert.success('success','data berhasil dihapus');
                    },
                    error(err){
                        loading.hide()
                        Alert.error('error','gagal menghapus data');
                        console.log(err);
                    }
                });
            });        
        })

          //run checkParams when document ready
        $(document).ready(function(){                        
            checkParams();
        });
    });

    function redirect(type,param){
        location.href = "<?=base_url()?>purchase/purchasing_product/"+type+"/"+param
    }

    var purchaseApp = new Vue({
        el : "#app",
        data : {
            form:{
                startDate : '',
                endDate : '',
                outlet : []
            },
            formPiutang:{
                data:{
                    totalSaldo:'',
                },
                inputs:{
                    purchase_id:{
                        value:'',
                        error:'',
                        allowClear: true,
                        required: true,
                    },
                    nominal:{
                        value:'',
                        error:'',
                        allowClear: true,
                        required: true,
                    },
                    bank:{
                        value:'cash',
                        error:'',
                        allowClear: true,
                        required: true,
                    }
                }
            },
            detail:{
                detailPrc:[]
            }
        },
        methods: {
            datatable(){
                $(function () {
                    $("#table-purchase").DataTable({
                        ajax: {
                            url: '<?=base_url()?>purchasing/purchasing_product/datatable',
                            type: "POST",
                            data:{
                                startDate : purchaseApp.form.startDate,
                                endDate : purchaseApp.form.endDate,
                                outlet : purchaseApp.form.outlet.toString(),
                            }
                        },
                        destroy : true,
                        serverSide: true,
                        scrollY: '55vh',
                        scrollCollapse: true,
                        // paging: false,
                        columns: [{
                                data: null,
                                class: "text-center",
                                orderable: false,
                                searchable: false,
                            },
                            {
                                data: "data_created",
                                render(data,type,row){
                                    return moment.unix(data/1000).local().format("DD/MM/YYYY")
                                }
                            },
                            {
                                data: "invoice",
                            },
                            {
                                data: "outlet_name",
                            },
                            {
                                data: "supplier_name",
                            },
                            {
                                data: "grand_total",
                                render(data,type,row){
                                    return currency(data)
                                }
                            },
                            // {
                            //     data: "bayar",
                            //     render(data,type,row){
                            //         return currency(row.bayar)
                            //     }
                            // },
                            // {
                            //     data: "bayar",
                            //     render(data,type,row){
                            //         return currency(row.debt_payment)
                            //     }
                            // },
                            {
                                data: "bayar",
                                render(data,type,row){
                                    var bayar = parseInt(row.bayar) + parseInt(row.debt_payment)
                                    return currency(bayar)
                                }
                            },
                            {
                                data: "total_retur",
                                orderable: false,
                                searchable: false,
                                render(data,type,row){
                                    return currency(row.total_retur)
                                }
                            },
                            {
                                data: "hutang", //hutang
                                orderable: false,
                                searchable: false,
                                render(data,type,row){
                                    var total_hutang = data-row.total_retur

                                    if(total_hutang < 0){
                                        total_hutang = 0
                                    }
                                    return currency(total_hutang)
                                    
                                }
                            },
                            {
                                data: "hutang", //piutang
                                orderable: false,
                                searchable: false,
                                render(data,type,row){
                                    var total_hutang = data-row.total_retur

                                    if(total_hutang > 0){
                                        total_hutang = 0
                                    }
                                    return currency((total_hutang*-1)-row.refund)
                                    
                                }
                            },
                            {
                                data: "status_lunas",
                                className: "text-center",
                                render: function(data, type, row) {
                                    var balace = row.hutang-row.total_retur
                                    if (balace<=0) {
                                        return '<span class="label label-success">Lunas</span>';
                                    }else{
                                        return '<span class="label label-default">Hutang</span>';
                                    }
                                    
                                }
                            },
                            {
                                data: "qty_arive",
                                className: "text-center",
                                orderable: false,
                                searchable: false,
                                render: function(data, type, row) {
                                    if (row.qty_stok != row.arive) {
                                        html = `<button class="btn btn-primary btn-xs" title="Partial" onclick="redirect('confirm',`+row.purchase_id+`)"><i class="fa fa-circle-o"></i></button>`
                                    }else{
                                        html = `<button class="btn btn-success btn-xs btn-terima" title="Completed"><i class="fa fa-check-square"></i></button>`
                                    }
                                    var total_hutang = row.hutang-row.total_retur+parseInt(row.refund) 
                                    // console.log(total_hutang);
                                    var btnMoney = ''
                                    if ( total_hutang <0) {
                                        btnMoney = `<button class="btn btn-primary btn-xs btn-piutang" title="Penerimaan Piutang"><i class="fa fa-money"></i></button>`
                                    }
                                    return html+' '+ btnMoney
                                    
                                }
                            },
                            {
                                data:"purchase_id",
                                class:"text-center",
                                orderable: false,
                                searchable: false,
                                render(data,type,row){
                                    let terima = `<button class="btn btn-success btn-xs btn-terima" title="Penerimaan" onclick="redirect('confirm',`+data+`)"><i class="glyphicon glyphicon-shopping-cart"></i></button>`

                                    let retur = `<button class="btn btn-danger btn-xs btn-retur" title="Pengembalian" onclick="redirect('retur',`+data+`)"><i class="glyphicon glyphicon-log-out"></i></button>`
                                    let detail = `<button class="btn btn-primary btn-xs btn-detail" title="detail"><i class="glyphicon glyphicon-th-list"></i></button>`
                                    let edit = `<button class="btn btn-warning btn-xs btn-edit" title="edit"><i class="fa fa-edit"></i></button>`
                                    let btnDel = `<button class="btn btn-danger btn-xs btn-del" title="Hapus"><i class="fa fa-trash"></i></button>`
                                    var html = ''
                                    if (row.qty_stok != row.arive) {
                                        html = retur+" "+detail 
                                    }else{
                                        html = retur+" "+detail
                                    }
                                    const editRole = "<?=$detail_access['edit_purchase']?>"
                                    const delRole = "<?=$detail_access['delete_purchase']?>"
                                    // console.log(delRole);
                                    // console.log("edit"+editRole);

                                    switch (true) {
                                        case (editRole==1 && delRole==1):
                                            return html+" "+edit+" "+btnDel
                                            break;
                                        case (editRole):
                                            return html+" "+edit
                                            break;
                                        default:
                                            return html+" "+edit
                                            break;
                                    }

                                    // if ("<?=$detail_access['edit_purchase']?>") {
                                    //     return html+" "+edit
                                    // }else{
                                    //     return html
                                    // }
                                    
                                }
                            }
                        ]
                    })
                });
               
            },
            onApply(){
                this.datatable()
            },
            localtime(){
                this.detail.data_created = moment.unix(this.detail.data_created/1000).local().format("DD/MM/YYYY")
            },
            numberFormat(val){
                let num =parseFloat(val)
                return number(num)
            },
            diskonBaris(){
                var diskonBaris = 0
                for (const i in this.detail.detailPrc) {
                    var disNominal = 0
                    if (this.detail.detailPrc[i].discount >0) {
                        if (this.detail.detailPrc[i].disc_type == 'percent') {
                            disNominal = (this.detail.detailPrc[i].qty_nota*this.detail.detailPrc[i].price_nota)/100*this.detail.detailPrc[i].discount
                        }else{
                            disNominal = this.detail.detailPrc[i].discount
                        }
                    }
                    this.detail.detailPrc[i].disNominal = disNominal
                    diskonBaris +=disNominal
                }
                this.detail.disBaris = diskonBaris
            },
            diskonNota(){
                var dis = this.detail.discount_total
                var disType = this.detail.discount_total
                var disNominal = 0
                if (disType == 'percent') {
                    disNominal = (this.detail.sub_total*dis)/100
                }else{
                    disNominal = dis
                }
                this.detail.disNominal = disNominal
            },
            pajakBaris(){
                var pajakBaris = 0
                var pjkArry = [];
                for (const i in this.detail.detailPrc) {
                    var pajak = 0
                    this.detail.detailPrc[i].pajakNom = 0;
                    if (this.detail.detailPrc[i].tax >0) {
                        if (this.detail.detailPrc[i].tax_type == 'percentage') {
                            pajak = ((this.detail.detailPrc[i].qty_nota*this.detail.detailPrc[i].price_nota)-this.detail.disBaris)/100*this.detail.detailPrc[i].tax
                        }else{
                            pajak = this.detail.detailPrc[i].tax
                        }
                        this.detail.detailPrc[i].pajakNom = pajak;

                        var tmpPajak = {
                            text : this.detail.detailPrc[i].tax_name,
                            nominal : pajak
                        }
                        pjkArry.push(tmpPajak)
                    }
                    pajakBaris +=pajak
                }
                this.detail.pajak = pajakBaris

                var newPjkArr = [];
                pjkArry.reduce(function(res, value) {
                    if (!res[value.text]) {
                        res[value.text] = { text: value.text, nominal: 0 };
                        newPjkArr.push(res[value.text])
                    }
                    res[value.text].nominal += parseInt(value.nominal);
                    return res;
                }, {});
                // console.log(newPjkArr);
                this.detail.pajakBaris = newPjkArr
            },
            reverseDate(param){
                return reverseDate(param)
            },
            insertPiutang(){
                if (!this.validate(this.formPiutang.inputs)) {
					return;
				}
                var data = {};
				for (var key in this.formPiutang.inputs) {
					if (this.formPiutang.inputs.hasOwnProperty(key)) {
						this.formPiutang.inputs[key].error = "";
						data[key] = this.formPiutang.inputs[key].value;
					}
				}
                data.nominal = clearFormat(data.nominal)
                // 
                $.ajax({
                    type: "POST",
                    url: "<?=base_url('purchasing/purchasing_product/insert_piutang')?>",
                    data: data,
                    dataType: "json",
                    beforeSend(){
                        loading.show();
                    },
                    success: function (response) {
                        loading.hide();
                        $('#modalPiutang').modal('hide')
                        Alert.success('Success','Data berhasil disimpan')
                        $("#table-purchase").DataTable().ajax.reload();
                    },
                    error(err){
                        loading.hide();
                        console.log(err);
                        Alert.error('Error','Gagal Menyimpan Data')
                    }
                });
            },
            showPurchaseDetail(id){
                console.log('show detail... id', id);
                $.ajax({
                    type: "post",
                    url: "<?=base_url()?>purchasing/purchasing_product/detail",
                    data: {id:id},
                    dataType: "json",
                    beforeSend:function(){
                        loading.show();
                    },
                    success: function (res) {
                        loading.hide()
                        console.log(res);
                        purchaseApp.detail= res.purchase
                        purchaseApp.detail.detailPrc = res.detail
                        $("#modalDetail").modal("show")
                        purchaseApp.localtime()
                        purchaseApp.diskonBaris()
                        purchaseApp.pajakBaris()
                    },
                    error(err){
                        loading.hide();
                        Alert.error();
                        console.log(err);
                    }
                });
            },
            currency(param){
                return currency(parseFloat(param)); 
            }
        },
        mounted() {
            setTimeout(() => {
                purchaseApp.form.startDate = $('#date-purchase').data('daterangepicker').startDate._d.valueOf()
                purchaseApp.form.endDate = $('#date-purchase').data('daterangepicker').endDate._d.valueOf()
                purchaseApp.datatable()
            }, 300);
        },
    })
    
    
</script>