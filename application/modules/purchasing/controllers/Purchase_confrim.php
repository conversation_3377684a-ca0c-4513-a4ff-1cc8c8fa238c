<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Purchase_confrim extends Auth_Controller {

	public function __construct()
    {
        parent::__construct();
        //proteksi halaman
        $this->load->model('purchase/Purchase_model');
		$this->load->model('outlet/Outlets_model'); //form
		$this->load->model('Purchase_confrim_model','purchase_confrim');
        $this->load->model('finance/M_set_jurnal_umum');
        

        $this->load->library('google/Pubsub');
    }

	public function index()
	{
		$link = base_url('purchasing/purchase_confrim').'/'; //URL dengan slash
        $data = array(
            'kolomID' => 'purchase_id', //nama kolom primary key pada tabel
            'ajaxActionDatatableList' => $link.'jsonConfrim',
            'ajaxActionJsonInConfrim' => $link.'jsonInConfrim'
        );

        $data['form_select_outlet'] = $this->Outlets_model->form_select();


		// $this->load->view('purchase/purchase/confrim/confrim_v',$data);
        $data['page_title'] = 'Purchase Confirm';
        $this->template->css('themes/plugins/select2-4.1.0/css/select2.min.css');
        // $this->template->js('themes/plugins/select2-4.1.0/js/select2.full.min.js','bottm');
        $this->template->js('themes/plugins/numeral/numeral.min.js');
        $this->template->js('themes/plugins/hot/moment/moment.js');
        $this->template->css('themes/plugins/datepicker/datepicker3.css');
        $this->template->js('themes/plugins/datepicker/bootstrap-datepicker.js');
        $this->template->view('purchasing/confirm_detail/confirm_detail_v',$data);
	}

    public function jsonConfrim()
    {
        header('Content-Type: application/json');
        $data = $this->purchase_confrim->jsonConfrim();
        $json_decode = json_decode($data); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $id=$a->id;
            $qty_stock=$a->qty_stok;
            $qty_confrim=$a->qty_confrim;
            $qty_notCOnfrim=$a->qty_notConfrim;

            $action ="<a href='javascript:void(0)' class='confrim' ><button class='btn-primary' style='border-color: transparent; color:#000' onclick='confrim({$id})'>Confirm</button> </a>";
            $temp_data=0;
            if ($qty_notCOnfrim !== '0') {
                 $temp_data = array(
                    'id' => $id,
                    'invoice' => $a->invoice,
                    'suplier' => $a->suplier,
                    'product_name' => $a->product_name,
                    'qty_stock' => formatDesimal($a->qty_stok,1),
                    'price_stock' => formatUang($a->price_stok),
                    'qty_confrim' => formatDesimal($a->qty_confrim,1),
                    'qty_notCOnfrim' =>formatDesimal($qty_notCOnfrim,1),
                    'total' => formatUang($a->total),
                    'outlet' => $a->outlet,
                    'action' =>$action,
                    'variant' => $a->variant
                );

                array_push($dataArray, $temp_data);
            }

        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */


    }

    public function jsonInConfrim()
    {
        header('Content-Type: application/json');
        $param =[
            'startDate' => $this->input->post('startDate'),
            'endDate' => $this->input->post('endDate'),
            'outlet' => $this->input->post('outlet'),     
        ];
        
        $data = $this->purchase_confrim->jsonInConfrim($param);
        $json_decode = json_decode($data); //pecah data json

        $dataArray = array();
        foreach ($json_decode->data as $a) {
            $qty_confirm = $a->qty_stok - $a->qty_notConfrim;
            $temp_data = array(
                'id' => $a->id,
                'date' => millis_to_localtime('d/m/Y H:i',$a->date),
                'invoice' => $a->invoice,
                'suplier' => $a->suplier,
                'product_name' => $a->product_name,
                'qty_stock' => formatDesimal($a->qty_stok,1),
                'price_stock' => formatUang($a->price_stok),
                'qty_confrim' => formatDesimal($qty_confirm,1),
                'qty_notConfrim' => formatDesimal($a->qty_notConfrim,1),
                'qty_arive' => formatDesimal($a->qty_arive,1),
                'total' => formatUang($a->total),
                'retur' => formatDesimal($a->retur,1),
                'outlet' => $a->outlet,
                'date_purchase'=>millis_to_localtime('d/m/Y H:i',$a->date_purchase),
                'admin_name'=>$a->admin_name,
                'variant' => $a->variant
            );
            array_push($dataArray, $temp_data);

        }

        //remake json data
        $draw_json = array(
            'draw' => $json_decode->draw,
            'recordsTotal' => $json_decode->recordsTotal,
            'recordsFiltered' => $json_decode->recordsFiltered,
            'data' => $dataArray
        );

        //tampilkan data json
        echo json_encode($draw_json);
        /* custom json output  end */
    }


    public function export_data($id)
    {
        // select purchase detail
        $data = $this->Purchase_products_model->json_export($id);
        // print_r($data[0]->purchase_fkid);die();
        // print_r($data);die();
        //$i=0;//index baris
        $jumlah_data=count($data);
        for ($i=0; $i < $jumlah_data; $i++) {
            $data_purchase=array(
                'purchase_product_fkid' => $data[$i]->purchase_products_id,
                'qty' => $data[$i]->qty_stok,
                'qty_notConfrim' =>'0',
                'qty_arive' =>$data[$i]->qty_stok,
                'retur' =>'null',
                'user' =>$data[$i]->user,
                'date_updated' =>$data[$i]->date_purchase,
                'date_created' =>$data[$i]->date_purchase,
            );
            $response = $this->Purchase_products_model->insert_confirm($data_purchase); //insert ke table purchse_product
        }

    }

}

/* End of file Purchase_confrim.php */
/* Location: ./application/controllers/purchase/purchase/Purchase_confrim.php */
?>
