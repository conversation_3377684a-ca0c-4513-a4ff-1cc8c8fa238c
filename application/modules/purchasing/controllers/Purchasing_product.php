<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Purchasing_product extends Auth_Controller {

    
    public function __construct()
    {
        parent::__construct();
        
        //Do your magic here
        $this->load->model('M_purchasing_product');
        $this->load->model('outlet/Outlets_model', 'Outlets_model');
        $this->load->model('outlet/Shift_model', 'Shift_model');
        $this->load->model('purchase/supplier/Supplier_model');
        $this->load->model('products/gratuity/Tax_gratuity_model', 'tax_gratuity');
        $this->load->model('outlet/Paymentmedia_bankaccount_model', 'Bankaccount_model');
        $this->load->model('products/ingridients/Unit_model');
        // $this->load->model('finance/M_set_jurnal_umum','set_jurnal');
         // Try to load the M_set_jurnal_umum model
         try {
            $this->load->model('finance/M_set_jurnal_umum', 'set_jurnal');
        } catch (Exception $e) {
            // If the model is not found, log the error and continue
            log_message('error', 'Could not load the M_set_jurnal_umum model: ' . $e->getMessage());
            echo "Could not load the M_set_jurnal_umum model";
        }
        
        $this->load->library('google/Pubsub');

        $this->env = getenv('CI_ENV');
        if (!empty($_ENV['CI_ENV'])) {
          $this->env = $_ENV['CI_ENV'];
        }
        if($this->env === '') {
          $this->env = 'development';
        }
        
    }
    
    public function index()
    {
        $detail_access = [
            'edit_purchase'=>true,
            'delete_purchase' => true,
            'change_date' => true
        ];
        if ($this->session->userdata('user_type')=='employee') {
			$data_access = ($this->session->userdata('role_web')['purchase/purchasing_product']['access']) ?? false;
            $edit = ($this->session->userdata('role_web')['purchase/purchasing_product']['edit']==0) ? false : true;
            $delete = ($this->session->userdata('role_web')['purchase/purchasing_product']['delete']==0) ? false : true;
			$data_access = json_decode($data_access);
            $detail_access = [
                'edit_purchase' => ($edit) ?? false,
                'delete_purchase' => ($delete) ?? false,
                'change_date' => ($data_access->change_date) ?? false
            ];
		}

        $data['page_title'] = 'Purchasing';
        $data['form_select_bank'] = $this->Bankaccount_model->form_select();
        $data['detail_access'] = $detail_access;
        $this->template->css('themes/plugins/select2-4.1.0/css/select2.min.css');
        $this->template->js('themes/plugins/select2-4.1.0/js/select2.full.min.js','bottm');
        $this->template->js('themes/plugins/numeral/numeral.min.js');
        $data['form_select_outlet'] = $this->Outlets_model->outlet_employe();
        $this->template->view('purchasing_product_v',$data);
    }

    public function datatable()
    {
        $param = [
            'start_date' => $this->input->post('startDate'),
            'end_date' => $this->input->post('endDate'),
            'outlet' => $this->input->post('outlet'),
            
        ];

        $data = $this->M_purchasing_product->data_purchase($param);
        echo $data;
    }

    public function form_purchase($type = null)
    {
        $detail_access = [
            'edit_purchase'=>true,
            'change_date' => true
        ];
        if ($this->session->userdata('user_type')=='employee') {
			$data_access = ($this->session->userdata('role_web')['purchase/purchasing_product']['access']) ?? false;
            $edit = ($this->session->userdata('role_web')['purchase/purchasing_product']['edit']==0) ? false : true;
			$data_access = json_decode($data_access);
            $detail_access = [
                'edit_purchase' => ($edit) ?? false,
                'change_date' => ($data_access->change_date) ?? false
            ];
		}

        $data['form_select_shift'] = $this->Shift_model->form_select();
        $data['form_select_outlet'] = $this->Outlets_model->outlet_employe();
        $data['form_select_supplier'] = $this->Supplier_model->form_select();
        $data['form_select_bank'] = $this->Bankaccount_model->form_select();
        $data['form_select_tax'] = $this->tax_gratuity->form_select();
        $data['form_select_unit_model'] = $this->Unit_model->form_select();
        $data['detail_access'] = $detail_access;
        // $this->template->css('https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css');
        $this->template->css('themes/plugins/select2-4.1.0/css/select2.min.css');
        $this->template->css('themes/plugins/datepicker/datepicker3.css');
        $this->template->js('themes/plugins/select2-4.1.0/js/select2.full.min.js','bottm');
        $this->template->js('themes/plugins/numeral/numeral.min.js');
        $this->template->js('themes/js/notify.js');
        $this->template->js('themes/plugins/datepicker/bootstrap-datepicker.js','bottm');
        $this->template->view('purchasing_product_form',$data);
    }

    public function get_product($type=null)
    {
        $param =[
            'key' => $this->input->post('key'),
            'outlet' => $this->input->post('outlet'),
            'type' => $type
        ]; 
        $data = $this->M_purchasing_product->data_product($param);
        // echo $this->db->last_query();die;
        foreach ($data as $key => $value) {
            $last_price = $this->db->select('price_nota')
            ->from("purchase_products")
            ->where("products_fkid",$value['product_detail_id'])
            ->order_by('purchase_products_id','desc')
            ->limit(1)
            ->get()->row();
            $last_price ?$data[$key]['last_price'] = $last_price->price_nota : $data[$key]['last_price'] = '0';
        }
        return $this->response_json($data);
    }

    public function conversion()
    {
        $product_id = $this->input->get('product_id');
        $unit_id = $this->input->get('unit_id');
        $data = $this->db->get_where('products_unit_convertion',["product_fkid"=>$product_id,"unit_fkid"=>$unit_id])->row();
        return $this->response_json($data);        
    }

    public function save()
    {
        $topic = 'purchase_'.$this->env;

        //maping data
        // $data = $this->input->post('detail');
        // print_r($data);die;
        $this->db->trans_start();
        $param = [
            "invoice" => $this->input->post('invoice'),
            "keterangan" => $this->input->post('keterangan'),
            "bayar" => $this->input->post('payment'),
            "status_lunas" => $this->input->post('status_lunas'),
            "supplier_fkid" => $this->input->post('supplier'),            
            "employee_fkid" => null,
            "admin_fkid" => $this->session->userdata('admin_id'),
            "user_input" => $this->session->userdata('user_name'),
            "outlet_fkid" => $this->input->post('outlet'),
            "grand_total" => $this->input->post('grand_total'),
            "hutang" => $this->input->post('hutang'),
            "jatuh_tempo" => $this->input->post('tempo'),
            "discount_total" => $this->input->post('discNota'),
            "total_pajak" => $this->input->post('pajak'),
            "sub_total" => $this->input->post('sub_total'),
            "type_discount" => $this->input->post('type_disc'),
            "pay_type" => $this->input->post('payment_media'),
            "payment_media_bank_fkid" => $this->input->post('payment_media_bank'),
            "shift_fkid" => $this->input->post('shift'),
            "data_status" => "on",
            "data_created" => current_millis(),
            "date_purchase" => $this->input->post('tgl'),
            
        ];
        if ($this->session->userdata('user_type')!='admin') {
            $param['employee_fkid'] = $this->session->userdata('user_id');
        }
        // print_r($param);die;
        // save purchase
        $this->db->insert('purchase', $param);
        // echo $this->db->last_query();die;
        
        $purchase_id = $this->db->insert_id();
        //update invoice number auto
        if (!$param['invoice']) {
            $supplier = $this->db->get_where('supplier',['supplier_id' => $param['supplier_fkid']])->row();            
            $month = millis_to_localtime('m',$param['data_created']);
            $invoice = "PCR-".substr(strtoupper($supplier->name), 0, 3).$month.$purchase_id;
            $this->db->where('purchase_id', $purchase_id);
            $this->db->update('purchase', ['invoice'=>$invoice]);
        }
        // maping data detail
        $detail = $this->input->post('detail');
        foreach ($detail as $det) {
            $tmp = [
                "purchase_fkid" => $purchase_id,
                "unit_fkid_nota" => $det["unit_nota"],
                "qty_nota" => $det["qty_nota"],
                "price_nota" => $det["harga_nota"],
                "unit_fkid_stok" => $det["unit_stock"],
                "qty_stok" => $det["qty_stock"],
                "price_stok" => $det["harga_stock"],
                "discount" => $det["disc"],
                "total" => $det["total"],
                "tot_dis" => $det["sub_total"],
                "data_status" => "on",
                "data_created" => date("Y-m-d H:i:s"),
                "products_fkid" => $det["product_id"],
                "qty_retur" => "0",
                "retur" => "no",
                "gratuity_fkid" => $det["pajak"],
                "tax" => !empty($det["tax_nominal"])?$det["tax_nominal"]:0, //nminal tax
                "depreciation" => "",
                "expired" => "",
                "disc_type" => !empty($det["dis_type"])?$det["dis_type"]:'',
                "tax_type" => !empty($det["tax_type"])?$det["tax_type"]:'',
            ];
            // save detail
            $this->db->insert('purchase_products', $tmp);
            $purchase_detail_id = $this->db->insert_id();
            $tmp_penerimaan = [
                "purchase_product_fkid" => $purchase_detail_id,
                "qty" => $det["qty_stock"],
                "qty_notConfrim" => $det["qty_stock"]-$det["diterima"],
                "qty_arive" => $det["diterima"],
                "user" => $this->session->userdata('user_name'),
                "date_confirm" => $param['date_purchase'],
                "date_created" => current_millis(),
                "date_updated" =>current_millis(),
            ];
            //insert penerimaan
            // if ($det["diterima"]) {
                $this->db->insert('purchase_confrim', $tmp_penerimaan);
                $cnfirm_id = $this->db->insert_id();
                 $this->pubsub->publish($topic, json_encode(array('purchase_product_fkid' => $purchase_detail_id, 'qty' => $det["diterima"], 'purchase_confirm_id' => $cnfirm_id)));
            // }

             // update harga beli prduct
            $this->db->where('product_detail_id', $det["product_id"]);
            $this->db->update('products_detail', ['price_buy'=>$det["harga_stock"]]);
        }
        $res= $this->db->trans_complete();
        if(getenv('CI_ENV') !== 'production'){
            $this->set_jurnal->jurnal_purchase_v2($purchase_id);
        }
        

        return $this->response_json($res);
        //insert jurnal umum
    }

    public function retur($id)
    {
        $this->template->css('themes/plugins/select2-4.1.0/css/select2.min.css');
        $this->template->css('themes/plugins/datepicker/datepicker3.css');
        $this->template->js('themes/plugins/select2-4.1.0/js/select2.full.min.js','bottm');
        $this->template->js('themes/plugins/numeral/numeral.min.js');
        $this->template->js('themes/plugins/numeral/numeral.min.js');
        $this->template->js('assets/plugins/moment/moment.min.js');
        $data['form_select_bank'] = $this->Bankaccount_model->form_select();
        $data['purchase'] = $this->M_purchasing_product->data_purchase_retur($id);
        $this->template->view('purchase_retur_v',$data);
    }

    public function detail_purchase()
    {
        $param = $this->input->get('id');
        $data = $this->M_purchasing_product->data_detail_purchase($param);
        // echo $this->db->last_query();die;
        
        // menambahkan index hisshowtory
        foreach ($data as $key=>$val) {
            $data[$key]["show"] =false; 
            $data[$key]["sisa_qty"] = $val['qty_arive']-$val['qty_retur'];
            $data[$key]["sisa_confirm"] = $val['qty_stok']-$val['qty_arive'];
        }
        return $this->response_json($data);
    }

    public function retur_history()
    {
        $id = $this->input->get('id');
        $data = $this->M_purchasing_product->data_retur($id);
        return $this->response_json($data);  
    }

    public function retur_save()
    {
        // print_r($this->input->post());die;
        $topic = 'purchase_retur_'.$this->env;
        $pubsub_data = [];
        $data_jurnal = [];
        $detail = [];
        $data_post = $this->input->post('dataSend');
        // $this->db->trans_start();
        foreach ($data_post['detail'] as $key) {
            if ($key['qty']) {
                $data_retur = [
                    'qty_retur' => floatval($key['qty']),
                    'qty_stok' => floatval($key['sisa']),
                    'purchase_product_fkid' => $key['id_detail'],
                    'keterangan_retur' => $key['keterangan'],
                    'admin_fkid' => $this->session->userdata('admin_id'),
                    'data_created' => current_millis(),
                    'tot_dis' => (floatval($key['qty'])*floatval($key['hpp']))+floatval($key['tax'])
                ];
                $res = $this->db->insert('retur_products', $data_retur); 
                // echo $this->db->last_query();die;
                               
                $retur_id = $this->db->insert_id();
                array_push($detail,$retur_id);
                $this->pubsub->publish($topic, json_encode(array('purchase_product_fkid' => $key['id_detail'], 'qty' => $key['qty'], 'retur_product_id' => $retur_id, 'purchase_confirm_id' => "")));
            }
            
        }
        // update status lunas jika ada kredit memo
        if ($data_post['kreditMemo'] != 0) {
            $this->db->where('purchase_id',$data_post['purchase_id']);            
            $this->db->update('purchase', ['status_lunas'=>'lunas']);
        }
        unset($data_post['detail']);
        $data_jurnal = $data_post;
        $data_jurnal['detail'] = $detail;
        if(getenv('CI_ENV') !== 'production') {
            $this->set_jurnal->jurnal_purchase_retur($data_jurnal);
        }        
        
        // $res = $this->db->trans_complete();
        if ($res) {
            $response = [
                "status" => "success",
                "msg" => "Data Berhasil Disimpan",
                "code" => 200
            ];
            return $this->response_json($response);
        }
        
    }

    public function confirm($id)
    {
        $data['purchase'] = $this->M_purchasing_product->data_purchase_retur($id);
        $this->template->css('themes/plugins/select2-4.1.0/css/select2.min.css');
        $this->template->css('themes/plugins/datepicker/datepicker3.css');
        $this->template->js('themes/plugins/select2-4.1.0/js/select2.full.min.js',);
        $this->template->js('themes/plugins/numeral/numeral.min.js');
        $this->template->js('themes/js/notify.js');
        $this->template->js('themes/plugins/datepicker/bootstrap-datepicker.js');
        $this->template->js('themes/plugins/hot/moment/moment.js');

        $this->template->js('themes/plugins/datetimepicker/js/bootstrap-datetimepicker.js', 'top');
        $this->template->css('themes/plugins/datetimepicker/css/bootstrap-datetimepicker.css');
        $this->template->view('purchase_confirm_v', $data);
    }

    public function confirm_history()
    {
        $id = $this->input->get('id');
        $data = $this->M_purchasing_product->data_confirm($id);
        
        foreach ($data as $key=>$val) {
            $data[$key]["show"] =false; 
        }
        return $this->response_json($data);
        
    }

    public function arive_save()
    {
        $topic = 'purchase_'.$this->env;
        $data_post = $this->input->post('data');
        // print_r($data_post);die;
        $totalArive = 0;
        $data_insert =[];
        foreach ($data_post as $key) {
            if ($key["qtyArive"]) {
               $data_insert = [
                    "purchase_product_fkid" => $key['id_detail'],
                    "qty" => $key["qty"],
                    "qty_notConfrim" => $key["qty"]-$key["qtyArive"],
                    "qty_arive" => $key["qtyArive"],
                    "user" => $this->session->userdata("user_name"),
                    "date_confirm" => $this->input->post('date'),
                    "date_created" => current_millis()
                ];
                $res = $this->db->insert('purchase_confrim', $data_insert);
                $confirm_id = $this->db->insert_id();
                $id_confirm[]=$confirm_id;

                // pub sub purchase confirm
                $this->pubsub->publish($topic, json_encode(array('purchase_product_fkid' => $data_insert['purchase_product_fkid'], 'qty' => $data_insert['qty_arive'], 'purchase_confirm_id' => $confirm_id)));

                $totalArive += $key["qtyArive"];
            }
            
        }
        
        // insert jurnal finance 
        if ($totalArive >0 && !in_array(getenv('CI_ENV'), array('production'))) {
            $this->set_jurnal->jurnal_purchase_confirm($id_confirm);
        }
        
        // if ($res) {
            $respon = [
                "status" => "success",
                "msg" => "Data berhasil Disimpan",
                "code" => 200,
            ];
            return $this->response_json($respon);
        // }
    
    }

    public function detail()
    {
        $id = $this->input->post('id');
        $result = [
            'purchase' => $this->M_purchasing_product->data_purchase_by_id($id),
            'detail' => $this->M_purchasing_product->data_detail_purchase($id)
        ];
        // $data_detail = $this->M_purchasing_product->data_detail_purchase($id);
        return $this->response_json($result);
        
    }

    public function insert_piutang()
    {
        $data_insert = [
            'purchase_fkid' => $this->input->post('purchase_id'),
            'nominal' => $this->input->post('nominal'),
            'payment_type' => $this->input->post('bank')!='cash' ? 'card':'cash',
            'bank_fkid' => $this->input->post('bank')!='cash' ? $this->input->post('bank'):'',
            'created_at' => current_millis(),            
        ];
        $res = $this->db->insert('purchase_refund', $data_insert);
        if ($res) {
           if(getenv('CI_ENV') !== 'production' && getenv('CI_ENV') !== 'staging') {
                $this->set_jurnal->jurnal_purchase_refund($data_insert);
            }   
            return $this->response_json($res);
        }
        
    }

    function edit($purchase_id) {
        $detail_access = [
            'edit_purchase'=>true,
            'change_date' => true
        ];
        if ($this->session->userdata('user_type')=='employee') {
			$data_access = ($this->session->userdata('role_web')['purchase/purchasing_product']['access']) ?? false;
            $edit = ($this->session->userdata('role_web')['purchase/purchasing_product']['edit']==0) ? false : true;
			$data_access = json_decode($data_access);
            $detail_access = [
                'edit_purchase' => ($edit) ?? false,
                'change_date' => ($data_access->change_date) ?? false
            ];
		}
        $data['form_select_shift'] = $this->Shift_model->form_select();
        $data['form_select_outlet'] = $this->Outlets_model->outlet_employe();
        $data['form_select_supplier'] = $this->Supplier_model->form_select();
        $data['form_select_bank'] = $this->Bankaccount_model->form_select();
        $data['form_select_tax'] = $this->tax_gratuity->form_select();
        $data['form_select_unit_model'] = $this->Unit_model->form_select();
        $data['data_purchase'] = $this->M_purchasing_product->data_purchase_by_id($purchase_id);
        $data["purchase_id"] = $purchase_id;
        $data['detail_access'] = $detail_access;
        
        // $this->template->css('https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css');
        $this->template->css('themes/plugins/select2-4.1.0/css/select2.min.css');
        $this->template->css('themes/plugins/datepicker/datepicker3.css');
        $this->template->js('themes/plugins/select2-4.1.0/js/select2.full.min.js',);
        $this->template->js('themes/plugins/numeral/numeral.min.js');
        $this->template->js('themes/js/notify.js');
        $this->template->js('themes/plugins/datepicker/bootstrap-datepicker.js');
        $this->template->js('themes/plugins/hot/moment/moment.js');
        $this->template->view('purchasing_edit_form_v',$data);
    }

    function get_detail_product_edit() {
        $outlet_id = $this->input->post('outlet_id');
        $data = $this->M_purchasing_product->data_detail_product($outlet_id);
        return $this->response_json($data);
    }

    function update() {
        file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " >> : update purchase data: " . json_encode($this->input->post()). "\n");
        
        $pubsub_data = array();
        $pubsub_data['purchase_id'] = $this->input->post('purchase_id');

        // update purchase
        $purchase_id = $this->input->post('purchase_id');
        $this->db->trans_start();
            $data_purchase = [
                "invoice" => $this->input->post('invoice'),
                "keterangan" => $this->input->post('keterangan'),
                "bayar" => $this->input->post('payment'),
                "status_lunas" => $this->input->post('status_lunas'),
                "supplier_fkid" => $this->input->post('supplier'),            
                "employee_fkid" => null,
                "admin_fkid" => $this->session->userdata('admin_id'),
                "user_input" => $this->session->userdata('user_name'),
                "outlet_fkid" => $this->input->post('outlet'),
                "grand_total" => $this->input->post('grand_total'),
                "hutang" => $this->input->post('hutang'),
                "jatuh_tempo" => $this->input->post('tempo'),
                "discount_total" => $this->input->post('discNota'),
                "total_pajak" => $this->input->post('pajak'),
                "sub_total" => $this->input->post('sub_total'),
                "type_discount" => $this->input->post('type_disc'),
                "pay_type" => $this->input->post('payment_media'),
                "payment_media_bank_fkid" => $this->input->post('payment_media_bank'),
                "shift_fkid" => $this->input->post('shift'),
                "data_status" => "on",
                "data_updated" => current_millis(),
                "date_purchase" => $this->input->post('tgl'),
            ];
            if ($this->session->userdata('user_type')!='admin') {
                $data_purchase['employee_fkid'] = $this->session->userdata('user_id');
            }
            $this->db->where('purchase_id', $purchase_id);
            $this->db->update('purchase', $data_purchase);

            // update debt payment
            $param = [
                'purchase_id' => $purchase_id,
                'sisa_pembayaran' => $this->input->post('sisa_pembayaran'),
            ];
            $this->update_debt_payment($param);

            // update detail purchase before edit
            $detail = $this->input->post('detail');
            foreach ($detail as $det) {
                $tmp = [
                    "purchase_fkid" => $purchase_id,
                    "unit_fkid_nota" => $det["unit_nota"],
                    "qty_nota" => $det["qty_nota"],
                    "price_nota" => $det["harga_nota"],
                    "unit_fkid_stok" => $det["unit_stock"],
                    "qty_stok" => $det["qty_stock"],
                    "price_stok" => $det["harga_stock"],
                    "discount" => $det["disc"],
                    "total" => $det["total"],
                    "tot_dis" => $det["sub_total"],
                    "data_status" => "on",
                    "data_created" => date("Y-m-d H:i:s"),
                    "products_fkid" => $det["product_id"],
                    "qty_retur" => "0",
                    "retur" => "no",
                    "gratuity_fkid" => $det["pajak"],
                    "tax" => !empty($det["tax_nominal"])?$det["tax_nominal"]:0, //nminal tax
                    "depreciation" => "",
                    "expired" => "",
                    "disc_type" => !empty($det["dis_type"])?$det["dis_type"]:'',
                    "tax_type" => !empty($det["tax_type"])?$det["tax_type"]:'',
                ];
                // check deleting data
                $old_data = $this->db->select('purchase_products_id')
                ->from("purchase_products")
                ->where("purchase_fkid",$purchase_id)
                ->get()->result_array();

                if (!!array_search($det['purchase_product_id'],$old_data)) {
                    $this->db->where('purchase_products_id', $det['purchase_product_id']);
                    $this->db->delete('purchase_products');

                    $this->db->where('purchase_products_fkid', $det['purchase_product_id']);
                    $this->db->delete('purchase_confrim');  
                    if($this->db->affected_rows()){
                        $pubsub_data["delete"]['purchase_confrim'][] = $det['purchase_product_id'];
                    }                    
                }
                
                // check new or old data
                if ($det['purchase_product_id']) {
                    // update detail purchase where detail purchase id with update data
                    $this->db->where('purchase_products_id', $det['purchase_product_id']);
                    $this->db->update('purchase_products', $tmp);                   
                    if($this->db->affected_rows()){                                                
                        $pubsub_data["update"]['purchase_products'][] = $det['purchase_product_id'];
                    }
                    
                    // PENERIMAAN
                    $arive = $this->db->select('*')
                        ->from('purchase_confrim')
                        ->where("purchase_product_fkid",$det['purchase_product_id'])
                        ->order_by('date_created','desc')
                        ->get()->result_array();

                    $total_arive=0;
                    foreach ($arive as $a) {
                        $total_arive += $a['qty_arive'];
                    }

                    // jika penambahan ( > dari diterima)
                    $totalData = count($arive);
                    if ($det['diterima'] >= $total_arive) {
                        $sisa = 0;
                        $selisih = $total_arive - $det['diterima']; 
                        foreach ($arive as $a) {
                            $totalData --;
                            $sisa = $det['diterima'] - $total_arive;
                            if ($totalData == 0) {
                                $this->db->where('purchase_confrim_id', $a['purchase_confrim_id']);
                                $this->db->update('purchase_confrim',[
                                    'qty_arive' => $a['qty_arive']+$sisa,
                                ]);  
                                if($this->db->affected_rows()){
                                    $pubsub_data["update"]['purchase_confrim'][] = $a['purchase_confrim_id'];
                                }                                
                            }
                        }
                    }else{
                        $sisa = 0;
                        $diterima = $det['diterima'];
                        $selisih = $total_arive - $det['diterima'];
                        foreach ($arive as $val) {
                            // $sisa = $val['qty_arive'] - $det['diterima'];
                            $diterima -= $val['qty_arive'];
                            if ($val['qty_arive'] >= $selisih && $selisih > 0) {
                                $this->db->where('purchase_confrim_id', $val['purchase_confrim_id']);
                                $this->db->update('purchase_confrim',[
                                    'qty_arive' => $val['qty_arive'] - $selisih,
                                ]);
                                if($this->db->affected_rows()){
                                    $pubsub_data["update"]['purchase_confrim'][] = $val['purchase_confrim_id'];
                                }                                
                            }elseif($val['qty_arive'] < $selisih && $selisih >0){
                                $this->db->where('purchase_confrim_id', $val['purchase_confrim_id']);
                                $this->db->update('purchase_confrim',[
                                    'qty' => 0,
                                    'qty_arive' => 0,
                                    'qty_notConfrim' => 0,
                                ]);
                                if($this->db->affected_rows()){
                                    $pubsub_data["update"]['purchase_confrim'][] = $val['purchase_confrim_id'];
                                }                                
                            }
                            $selisih -= $val['qty_arive'];
                        }
                    }
                    
                }else{ // if add new items purchase
                    // save detail
                    $this->db->insert('purchase_products', $tmp);
                    $purchase_detail_id = $this->db->insert_id();
                    $tmp_penerimaan = [
                        "purchase_product_fkid" => $purchase_detail_id,
                        "qty" => $det["qty_stock"],
                        "qty_notConfrim" => $det["qty_stock"]-$det["diterima"],
                        "qty_arive" => $det["diterima"],
                        "user" => $this->session->userdata('user_name'),
                        "date_confirm" => $data_purchase['date_purchase'],
                        "date_created" => current_millis(),
                        "date_updated" => current_millis(),
                    ];
                    //insert penerimaan
                    if ($det["diterima"]) {
                        $this->db->insert('purchase_confrim', $tmp_penerimaan);
                        $pubsub_data["add"]['purchase_confrim'][] = $this->db->insert_id();
                    }
                }
                
                // update harga beli prduct
                $this->db->where('product_detail_id', $det["product_id"]);
                $this->db->update('products_detail', ['price_buy'=>$det["harga_stock"]]);
            }

        $res= $this->db->trans_complete();
        // update jurnal
        if (!in_array(getenv('CI_ENV'), array('production'))) {
            $this->set_jurnal->jurnal_purchase_update($purchase_id);
        }

        file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " >> : update purchase status " . $res. "\n");

        if($res){
            file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " >> : pubsub data: " .json_encode($pubsub_data). "\n");        
            try {
                $this->pubsub->publish('purchase-confirm-update-'.$this->env, json_encode($pubsub_data));
            } catch (Exception $e) {
                file_put_contents("php://stderr", basename(__FILE__) . ":" . __LINE__ . " >> : [ERROR OCCURED] failed publish pubsub, err: " .$e->getMessage(). "\n");
            }
        }
        
        return $this->response_json($res);
    }

    function update_debt_payment($data) {
        // update data retur
        $data_debt = $this->db->select('sp.*, sum(sp.nominal) as total_nominal')
        ->from('debt_payment sp')
        ->where('purchase_fkid',$data['purchase_id'])
        ->group_by('sp.debt_payment_id')
        ->order_by('time_created','desc')
        ->get()->result_array();
        
        if ($data_debt) {
            $total_dbt = $data_debt[0]['total_nominal'];
            $sisa = $data['sisa_pembayaran'];
            foreach ($data_debt as $val) {
                if ($sisa >= $val['nominal'] && $sisa > 0) {
                    $this->db->where('debt_payment_id', $val['debt_payment_id']);
                    $this->db->update('debt_payment', [
                        'nominal' => 0
                    ]);
                }elseif ($sisa < $val['nominal'] && $sisa > 0) {
                    $this->db->where('debt_payment_id', $val['debt_payment_id']);
                    $this->db->update('debt_payment', [
                        'nominal' => $val['nominal'] - $sisa
                    ]);
                }
                $sisa -= $val['nominal'];
            }
        }
        $this->update_jurnal_debt($data['purchase_id']);
    }

    function update_jurnal_purchase($purchase_id = null) {
        // echo "test";die;
        $data_purchase = $this->db->select('purchase_id')
        ->from('purchase')
        ->where('admin_fkid',10)
        ->where('date_purchase >=1704042000000')
        ->get()->result_array();

        $this->db->trans_start();
        foreach ($data_purchase as $key => $value) {
            $this->set_jurnal->add_to_jurnal_purchase($value['purchase_id']);
        }
        $res= $this->db->trans_complete();
        return $this->response_json($res);
    }

    function update_jurnal_debt($purchase_id) {
        $data_debt = $this->db->select('debt_payment_id')
        ->from('debt_payment')
        ->where('purchase_fkid',$purchase_id)
        ->get()->result_array();

        $this->db->trans_start();
        foreach ($data_debt as $key => $value) {
            $this->set_jurnal->jurnal_debit_pay($value['debt_payment_id']);
        }
        $res= $this->db->trans_complete();
        // return $this->response_json($res);
        
    }

    function delete() {
        $prc_id = $this->input->post('purchase_id');
        // print_r($prc_id);
        $this->db->trans_start();
        // del jurnal prc
        $this->db->where('trans_id', $prc_id);
        $this->db->where_in('trans_type',[2,14]);
        $this->db->delete('finance_jurnal_umum');
        
        // del jurnal prc_confirm
        //data confirmm
        $data_cofirm = $this->db->select('purchase_confrim_id')
        ->from('purchase_confrim pc')
        ->join('purchase_products pp','pp.purchase_products_id=pc.purchase_product_fkid','left')
        ->where('pp.purchase_fkid',$prc_id)
        ->get()->result_array();
    
        foreach ($data_cofirm as $confirm) {
            // del all purchas confirm history jurnal
            $this->db->where('trans_type',5);
            $this->db->where("SUBSTRING_INDEX(trans_id,'.',-1)", $confirm['purchase_confrim_id']);
            $this->db->delete('finance_jurnal_umum');

            //del prc confirm
            $this->db->where('purchase_confrim_id', $confirm['purchase_confrim_id']);
            $this->db->delete('purchase_confrim');            
        }
            
        //del jurnal debt pay
        $data_debt = $this->db->select('debt_payment_id')
        ->from('debt_payment')
        ->where('purchase_fkid',$prc_id)
        ->get()->result_array();

        foreach ($data_debt as $debt) {
            // delete jurnal debt
            $this->db->where('"SUBSTRING_INDEX(trans_id,'.',-1)"', $debt['debt_payment_id']);
            $this->db->where('trans_type', 14);
            $this->db->delete('finance_jurnal_umum');            
        }

        //del debt
        $this->db->where('purchase_fkid', $prc_id);
        $this->db->delete('debt_payment');

        //del prc detail
        $this->db->where('purchase_fkid', $prc_id);
        $this->db->delete('purchase_products');
        
        // del purchase
        $this->db->where('purchase_id', $prc_id);
        $this->db->delete('purchase');        
        


        $res= $this->db->trans_complete();

        return $this->response_json($res);
    }

}

/* End of file Purchasing.php */
