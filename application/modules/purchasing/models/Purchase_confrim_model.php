<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Purchase_confrim_model extends CI_Model {

	public $table = 'purchase_confrim';
    public $id = 'purchase_confrim_id';
    public $order = 'DESC';
	public function __construct()
	 {
	 	parent::__construct();
	 	//Do your magic here
	 }

	public function jsonConfrim()
	{
		$this->datatables->select("
			pr.invoice,
			s.name as suplier,
			p.name as product_name,
			pp.qty_stok,
			pp.purchase_products_id ,
			pp.price_stok,
			pp.total,
			o.name as outlet,
			ifnull(pdv.variant_name,'') as variant,
			max(pc.Purchase_confrim_id) as id,
			sum(pc.qty_arive) as qty_confrim,
			min(pc.qty_notConfrim) as qty_notConfrim,
		");
		$this->datatables->from('products p');
		$this->datatables->join('products_detail pd','p.product_id=pd.product_fkid');
		$this->datatables->join('products_detail_variant pdv', 'pdv.variant_id = pd.variant_fkid', 'left');
		$this->datatables->join('purchase_products pp','pp.products_fkid=pd.product_detail_id');
		$this->datatables->join('purchase pr','pr.purchase_id=pp.purchase_fkid');
		$this->datatables->join('supplier s','pr.supplier_fkid=s.supplier_id');
		$this->datatables->join('purchase_confrim pc','pc.purchase_product_fkid=pp.purchase_products_id');
		$this->datatables->join('outlets o','pr.outlet_fkid=o.outlet_id');
		$this->datatables->where('pr.admin_fkid',$this->session->userdata('admin_id'));
		// $this->datatables->where('pp.retur','no');
		$this->db->group_by('pp.purchase_products_id');
		$this->db->group_by('o.outlet_id');
		return $this->datatables->generate();
	}

	public function jsonInConfrim($param)
	{
		$this->datatables->select("
			pr.invoice,
			s.name as suplier,
			p.name as product_name,
			pp.qty_stok,
			pp.purchase_products_id as id,
			pp.price_stok,
			pp.total,
			pc.qty_notConfrim,
			pc.date_confirm as date,
			pc.retur,
			o.name as outlet,
			pc.qty_arive,
			ifnull(pdv.variant_name,'') as variant,
			pr.data_created as date_purchase,

			(CASE WHEN (pc.employe_fkid is not null)
            THEN (SELECT name FROM employee WHERE employee_id = pc.employe_fkid)
            ELSE (SELECT name FROM admin WHERE admin_id=pr.admin_fkid )
            END) AS admin_name
		");
		$this->datatables->from('products p');
		$this->datatables->join('products_detail pd','p.product_id=pd.product_fkid');
		$this->datatables->join('products_detail_variant pdv', 'pdv.variant_id = pd.variant_fkid', 'left');
		$this->datatables->join('purchase_products pp','pp.products_fkid=pd.product_detail_id');
		$this->datatables->join('purchase pr','pr.purchase_id=pp.purchase_fkid');
		$this->datatables->join('supplier s','pr.supplier_fkid=s.supplier_id');
		$this->datatables->join('purchase_confrim pc','pc.purchase_product_fkid=pp.purchase_products_id');
		$this->datatables->join('outlets o','pr.outlet_fkid=o.outlet_id');
		$this->datatables->join('employee', 'employee.employee_id = pr.employee_fkid', 'left');
		$this->datatables->join('admin', 'admin.admin_id = pr.admin_fkid', 'left');
		$this->datatables->where('pr.admin_fkid',$this->session->userdata('admin_id'));
		if (!empty($param['outlet'])) {
            $this->datatables->where_in("o.outlet_id",$param['outlet']);
        }

        if (!empty($param['startDate'])) {
            $this->datatables->where("pr.data_created >=", $param['startDate']);
        }
        if (!empty($param['endDate'])) {
            $this->datatables->where("pr.data_created <=", $param['endDate']);
        } 
		$this->datatables->group_by('pc.purchase_confrim_id');
		
		return $this->datatables->generate();
	}

	// get data by id
    function get_by_id($id)
    {
    	$this->db->select("
    		pc.purchase_confrim_id as id,
    		pc.purchase_product_fkid,
    		pc.qty_notConfrim,
    		pc.date_created,
    		pc.qty,
    		p.name as product_name

    		");
    	$this->db->from("purchase_confrim pc");
    	$this->db->join('purchase_products pp','pp.purchase_products_id=pc.purchase_product_fkid','left');
    	$this->db->join('products_detail pd','pd.product_detail_id=pp.products_fkid','left');
    	$this->db->join('products p','p.product_id=pd.product_fkid','left');
        $this->db->where("pc.purchase_confrim_id", $id);
        $this->db->where('pc.data_status', 'on'); //cari hanya data aktif
        return $this->db->get($this->table)->row();
    }

    // get data by id
    function get_by_id_retur($id)
    {
    	$this->db->select("
    		pc.purchase_confrim_id as id,
    		pc.purchase_product_fkid,
    		pc.qty_notConfrim,
    		pc.date_created,
    		pc.qty,

    		");
    	$this->db->from("purchase_confrim pc");

        $this->db->where("pc.purchase_product_fkid", $id);
        $this->db->where('pc.data_status', 'on'); //cari hanya data aktif
        return json_encode($this->db->get()->result_array());
    }

		public function fetch_last_insert_id(){
			return $this->db->insert_id();
		}

	function insert($dataConfrim)
    {
        $dataConfrim[$this->id] = null;
        $dataConfrim['data_status'] = 'on';
        return $this->db->insert($this->table, $dataConfrim);
    }

    function update($id, $data)
    {
        $this->db->where($this->id, $id);
        $this->db->where('data_status', 'on'); //cari hanya data aktif
        // $this->db->where('admin_fkid', $this->session->userdata('admin_id')); //ambil berdasarkan owner
        return $this->db->update($this->table, $data);
    }

    //cek apakah barnag retur
    public function exsits($id)
    {
        $this->db->where('transfer_product_fkid',$id);
        $query = $this->db->get('transfer_confirm');
        if ($query->num_rows() > 0){
            return true;
        }
        else{
            return false;
        }
    }

}

/* End of file Purchase_confrim_model.php */
/* Location: ./application/models/purchase/purchase_confrim/Purchase_confrim_model.php */

?>
