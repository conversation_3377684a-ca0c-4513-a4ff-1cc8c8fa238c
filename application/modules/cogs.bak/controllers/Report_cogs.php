<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Report_cogs extends Auth_Controller {

	public function __construct()
	{
		parent::__construct();
		$this->load->model('outlet/Outlets_model');
		$this->load->model('products/products_category/Products_category_model', 'category_model');
		$this->load->model('M_report_cogs','m_cogs');
	}

	public function index()
	{
		$data['form_select_outlet'] = $this->Outlets_model->outlet_employe();
		$data['from_select_category'] = $this->category_model->form_select();
		$this->template->view('report_cogs_v',$data); //menampilkan halaman
	}

	public function get_data_v2($startDate,$endDate,$timeZone,$category,$outlet)
	{
		$dataSales = $this->m_cogs->data_cogs_v2($startDate,$endDate,$timeZone,$category,$outlet);

		$data = array();
		$pd_id_tpm = [];
		foreach ($dataSales as $key => $value) {
			$qty = $value['qty'] - $value['qty_void'];
			$subTotal = $value['total_price'];
			$totalDis = $value['discount_sd']+$value['discount_sales']+$value['discount_tax']+$value['voucher_tax'];
			$grandTotal = $subTotal-$totalDis+$value['tax']+$value['service_tax']-$value['promo'];
			$totalHpp = $value['total_hpp'];
			$avgHpp = $value['total_hpp'] / $qty;
			$avgPrice = $value['total_price'] / $qty;
			// $subTotal = $value['sub_total'] -
			// $profit = $grandTotal-$totalHpp;
			$tmpData = array(
				'pd_id' => $value['product_detail_id'],
				'nama_produk' => $value['product_name'],
				'sku' => $value['sku'].$value['variant_sku'],
				'outlet' => $value['outlet_name'],
				'sub_category' => $value['sub_category'],
				'category' => $value['category'],
				'qty_sales' => $qty,
				'hpp' => $avgHpp,
				'total_hpp' => $totalHpp,
				'harga_jual' => $avgPrice,
				'sub_total' => $grandTotal,
				'tax' => $value['tax'],
				'disc' => $totalDis,
				'promo' => $value['promo'],
				'qty_purchase' => '0'
			);
			array_push($data, $tmpData);
			array_push($pd_id_tpm,$value['product_detail_id']);
		}

		$pd_id = implode("','",$pd_id_tpm);

		// get purchase by date and pd_id
		// $purchase = $this->m_cogs->purchase($startDate,$endDate,$timeZone);
		// foreach ($purchase as $p) {
		// 	foreach ($data as $idx => $d) {
		// 		if ($d['pd_id']==$p['pd_id']) {
		// 			$data[$idx]['hpp'] = $p['price'];
		// 			$data[$idx]['total_hpp'] = $p['price']*$p['qty'];
		// 			$data[$idx]['qty_purchase'] = $p['qty'];
		// 		}
		// 	}
		// }

		// print_r($purchase);die;
		$draw_json = array(
        	'data' => $data
        );
		return $this->output
            ->set_content_type('application/json')
            ->set_status_header(200)
            ->set_output(json_encode($draw_json));
	}

	public function get_data($startDate,$endDate,$timeZone,$category,$outlet)
	{
		$dataSales = $this->m_cogs->data_cogs($startDate,$endDate,$timeZone,$category,$outlet);

		$data = array();
		$pd_id_tpm = [];
		foreach ($dataSales as $key => $value) {
			$qty = $value['qty'] - $value['qty_void'];
			$subTotal = $qty * $value['price'];
			$totalDis = $value['discout_sd']+$value['discount_sales']+$value['discount_tax'];
			$grandTotal = $subTotal-$totalDis+$value['tax']+$value['service_tax']-$value['promo']-$value['voucher_tax'];
			$totalHpp = $qty * $value['hpp'];
			// $subTotal = $value['sub_total'] -
			// $profit = $grandTotal-$totalHpp;
			$tmpData = array(
				'pd_id' => $value['product_detail_id'],
				'nama_produk' => $value['product_name'],
				'sku' => $value['sku'].$value['variant_sku'],
				'outlet' => $value['outlet_name'],
				'sub_category' => $value['sub_category'],
				'category' => $value['category'],
				'qty_sales' => $qty,
				'hpp' => '0',
				'total_hpp' => $totalHpp,
				'harga_jual' => $value['price'],
				'sub_total' => $value['sub_total'],
				'tax' => $value['tax'],
				'disc' => $totalDis,
				'promo' => $value['promo'],
				'qty_purchase' => '0'
			);
			array_push($data, $tmpData);
			array_push($pd_id_tpm,$value['product_detail_id']);
		}

		$pd_id = implode("','",$pd_id_tpm);

		// get purchase by date and pd_id
		// $purchase = $this->m_cogs->purchase($startDate,$endDate,$timeZone);
		// foreach ($purchase as $p) {
		// 	foreach ($data as $idx => $d) {
		// 		if ($d['pd_id']==$p['pd_id']) {
		// 			$data[$idx]['hpp'] = $p['price'];
		// 			$data[$idx]['total_hpp'] = $p['price']*$p['qty'];
		// 			$data[$idx]['qty_purchase'] = $p['qty'];
		// 		}
		// 	}
		// }

		// print_r($purchase);die;
		$draw_json = array(
        	'data' => $data
        );
		return $this->output
            ->set_content_type('application/json')
            ->set_status_header(200)
            ->set_output(json_encode($draw_json));
	}

}

/* End of file Report_cogs.php */
/* Location: ./application/modules/cogs/controllers/Report_cogs.php */
