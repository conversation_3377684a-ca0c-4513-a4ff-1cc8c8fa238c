<style type="text/css">
    /* Ensure that the demo table scrolls */
    th, td { white-space: nowrap; }
    div.dataTables_wrapper {
        margin: 0 auto;
    }
    div.dt-buttons {
        float: right;
        margin-left:10px;
    }
</style>
<div class="container-fluid">
    <div class="row">
        <div class="col-sm-4">
            <div><h3>Report <small>COGS</small></h3></div>
        </div>
        <div class="col-sm-8">
            <div class="pull-right" style="text-align: right;">
                <div class="btn-group label-tabele">
                    <button type="button" class=" btn btn-info" id="dateProduct" >
                        <span>
                            <i class="glyphicon glyphicon-calendar"></i> Date
                        </span>
                        <i class="caret"></i>
                    </button>
                </div>
                <div class="btn-group label-tabele">
                    <select class="form-control btn btn-info" id="select_outlet" name="select_outlet" >
                        <option value="0" selected="">ALL Outlet</option>
                        <?php foreach ($form_select_outlet as $a): ?>
                            <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
                        <?php endforeach ?>
                    </select>
                </div>
                <div class="btn-group label-tabele">
                    <select class="form-control btn btn-info"  id="select_category" name="select_category">
                        <option value="0" selected="">ALL category</option>
                        <?php foreach ($from_select_category as $a): ?>
                            <option value="<?=$a->product_category_id ?>"><?=htmlentities($a->name) ?></option>
                        <?php endforeach ?>
                    </select>
                </div>
                <div class="btn-group label-tabele">
                    <button class="btn btn-primary btn-block" id="btnProduct" type="button">Apply</button>
                </div>
            </div>
        </div>
    </div>
    <!--content-->
    <div class="row">
        <div class="col-sm-12">
            <div class="content-uniq table-responsive">
                <table id="tableProduct" class="table table-striped table-report" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>NO</th>
                            <th>Outlet</th>
                            <th>SKU</th>
                            <th>Category</th>
                            <th>Sub Category</th>
                            <th>Nama Product</th>
                            <th>Qty Sales</th>
                            <th>AVG Price</th>
                            <th>Qty Purchase</th>
                            <th>AVG Hpp</th>
                            <th>Disc & Voucher</th>
                            <th>Tax</th>
                            <th>Promo</th>
                            <th>Total Hpp</th>
                            <th>Sub Total</th>
                            <th>Profit</th>
                        </tr>
                    </thead>
                    <tfoot>
                        <th>Total</th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>
</div>


<script type="text/javascript">
$(document).ready(function() {

    //inisialisasi
    var d = new Date();
    var e=d.getTimezoneOffset();
    var timeZone = e*60*-1;
    var start = moment().subtract(30, 'days');
    var end = moment();
    var outlet=$("#select_outlet").val();
    var shift=0;
    var productCategory=0;
    var startDate = moment().subtract(30, 'days').format('YYYY-MM-DD');
    var endDate = moment().format('YYYY-MM-DD');
    $('#dateProduct span').html(start.format('MMMM D, YYYY')+' - '+end.format('MMMM D, YYYY'));
     tableProduct(startDate,endDate,timeZone,productCategory,outlet);

    $('#btnProduct').on('click',function(){
        $('#tableProduct').DataTable().destroy(); //hapus data table
        $("#tableProduct > tbody > tr").remove(); //hapus content datatable
        setTimeout(function() {
            tableProduct(startDate,endDate,timeZone,productCategory,outlet);
        }, 10);
       // $("#tableProduct").DataTable().ajax.reload()

    });

   //select outlet start
   $('#select_outlet').focus(function(){
        outlet=$(this).val();
    }).bind('change',function(){
        if ($(this).val()==0) {
            swal({
                title: 'Warning!',
                text: "Proses ini mungkin membutuhkan sedikit waktu, pastikan koneksi internet anda dalam keadaan baik",
                type: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Lanjut'
            }).then((result) => {
                if (result.value) {
                    outlet=$(this).val();
                }else  {
                    $(this).val(outlet);
                    return false;
                }
            })
        }else{
            outlet=$(this).val();
        }

    });

   //select category start
   $('#select_category').on('change',function(){
        productCategory=$(this).val();
    });

   //select date start
   $('#dateProduct').daterangepicker({
        ranges: {
            //'None': [moment().startOf('days'), moment().endOf('days')],
            'Today': [moment(), moment()],
            'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
            'Last 7 Days': [moment().subtract(6, 'days'), moment()],
            'Last 30 Days': [moment().subtract(29, 'days'), moment()],
            'This Month': [moment().startOf('month'), moment().endOf('month')],
            'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
        },
        startDate: moment().subtract(29, 'days'),
        endDate: moment(),
        opens: "left"
    },
    function(start, end) {
        $('#dateProduct span').html(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));
         startDate=start.format('YYYY-MM-DD');
         endDate=end.format('YYYY-MM-DD');

    });



function tableProduct(startDate,endDate,timeZone,productCategory,outlet){
    loading_show();
    //to format number from footer
    var numFormat = $.fn.dataTable.render.number( formatAngka(),  ).display;
    var formatDes = $.fn.dataTable.render.number( '\.', ',', 2,  ).display;

    var t = $("#tableProduct").DataTable({
        dom:"<'row'<'col-sm-6'l><'col-sm-6'Bf>>" +
            "<'row'<'col-sm-12'tr>>" +
            "<'row'<'col-sm-5'i><'col-sm-7'p>>",
         initComplete: function() {
            var api = this.api();
            $('#tableProduct_filter input')
                    .on('.DT')
                    .on('keyup.DT', function(e) {
                        api.search(this.value).draw();
            });
        },
        scrollY:'auto',
        scrollX:true,
        // "order": [[ 6, "desc" ]],

        buttons: [
            {
                extend: 'collection',
                text: 'EXPORT',
                buttons: [
                    {
                        extend: 'pdfHtml5',
                        orientation: 'landscape',
                        pageSize: 'legal',
                        title: "REPORT COGS",
                        filename:"Report COGS",
                        customize: function (doc) {
                            doc.content[1].table.widths = Array(doc.content[1].table.body[0].length + 1).join('*').split('');
                        },
                        exportOptions: {
                            columns: [ 1,2,3,4,5,6,7,9,10,11,12,13,14,15]
                        }

                    },
                    {
                        extend: 'excelHtml5',
                        orientation: 'landscape',
                        pageSize: 'legal',
                        title: "REPORT COGS",
                        filename:"Report COGS",
                        exportOptions: {
                            columns: [ 1,2,3,4,5,6,7,9,10,11,12,13,14,15],
                            format: {
                                body: function ( data, row, column, node ) {
                                    data = $('<p>' + data + '</p>').text();
                                    let symbl = '<?php echo $this->session->userdata('currency_symbol') ?>';
                                    if ((data.startsWith(symbl) || data.startsWith('-'+symbl)) && $.isNumeric(data.replace( /[<?php echo $this->session->userdata('currency_symbol') ?>.,]/g, '' ))) {
                                      // data = data.replace(/[^0-9\.,-]+/g,""); //replace any character except number, comma, dot, and strip
                                      data = data.replace(/(,|\.)[0-9]{2}$/g,""); //replace two number after comma or dot
                                      return data.replace(/[^0-9-]/g,""); //replace everything except numbers
                                    }

                                    return data;
                                }

                            }
                        }

                    },

                ],

            }
        ],
        //sorting curent
        columnDefs: [
           { type: 'formatted-num', targets: [6,7,8,9,10,11,12,13,14,15] },
           {
                "targets": [ 8 ],
                "visible": false
            }
        ],


        "footerCallback": function ( row, data, start, end, display ) {
            var api = this.api(), data;

            // Remove the formatting to get integer data
            var intVal = function ( i ) {
                return typeof i === 'string' ?
                    i.replace( /[<?php echo $this->session->userdata('currency_symbol') ?>.,]/g, '' )*1 :
                    typeof i === 'number' ?
                        i : 0;
            };

            // Total over all pages
            qty = api.column( 6, {page:'current'} ).data().reduce( function (a, b) {
                return intVal(a) + intVal(b);
            }, 0 );
            price = api.column( 7, {page:'current'} ).data().reduce( function (a, b) {
                return intVal(a) + intVal(b);
            }, 0 );
            qty_purchase = api.column( 8, {page:'current'} ).data().reduce( function (a, b) {
                return intVal(a) + intVal(b);
            }, 0 );
            hpp = api.column( 9, {page:'current'} ).data().reduce( function (a, b) {
                return intVal(a) + intVal(b);
            }, 0 );
            disc = api.column( 10, {page:'current'} ).data().reduce( function (a, b) {
                return intVal(a) + intVal(b);
            }, 0 );
            tax = api.column( 11, {page:'current'} ).data().reduce( function (a, b) {
                return intVal(a) + intVal(b);
            }, 0 );
            promo = api.column( 12, {page:'current'} ).data().reduce( function (a, b) {
                return intVal(a) + intVal(b);
            }, 0 );
            totalHpp = api.column( 13, {page:'current'} ).data().reduce( function (a, b) {
                return intVal(a) + intVal(b);
            }, 0 );
            subTotal = api.column( 14, {page:'current'} ).data().reduce( function (a, b) {
                return intVal(a) + intVal(b);
            }, 0 );


            // Update footer
            $( api.column( 6 ).footer() ).html(numFormat(qty) );
            $( api.column( 7 ).footer() ).html(currency(price));
            $( api.column( 8 ).footer() ).html(numFormat(qty_purchase));
            $( api.column( 9 ).footer() ).html(currency(hpp));
            $( api.column( 10 ).footer() ).html(currency(disc));
            $( api.column( 11 ).footer() ).html(currency(tax));
            $( api.column( 12 ).footer() ).html(currency(promo));
            $( api.column( 13 ).footer() ).html(currency(totalHpp));
            $( api.column( 14 ).footer() ).html(currency(subTotal));
            $( api.column( 15 ).footer() ).html(currency(subTotal-totalHpp));
        },


        //to table scroll left top
        scrollY:        "440px",
        scrollX:        true,
        scrollCollapse: true,
        paging:         true,
        ordering:       true,
        processing: false,
        serverSide: false,
        initComplete: function() {
                loading_hide();
            },
        ajax: {
            "url": "<?=base_url()?>cogs/report_cogs/get_data_v2/"+startDate+"/"+endDate+"/"+timeZone+"/"+productCategory+"/"+outlet,
            "type": "post",
            error(err){
                Alert.error('Error',"Terjadi Kesalahan");
                console.log(err)
                loading_hide()
            }
        },
        columns: [
            {"data": null},
            {"data": "outlet"},
            {"data": "sku"},
            {"data": "category"},
            {"data": "sub_category"},
            {
                "data": "nama_produk",
                // render(data,type,row){
                //     let product = '<span title="'+data+'">'+data+'</span>'
                //     if (data.length > 10) {
                //         product = '<span title="'+data+'">'+data.substr( 0, 10 )+"..."+'</span>'
                //     }
                //     return product
                // }
            },
            {"data": "qty_sales"},
            {
                "data": "harga_jual",
                render: function(data, type, row) {
                    return currency(row.harga_jual);
                }
            },
            {
                "data": "qty_purchase",
            },
            {
                "data": "hpp",
                render: function(data, type, row) {
                    return currency(row.hpp);
                }
            },
            {
                "data": "disc",
                render: function(data, type, row) {
                    return currency(row.disc);
                }
            },
            {
                "data": "tax",
                render: function(data, type, row) {
                    return currency(row.tax);
                }
            },
            {
                "data": "promo",
                render: function(data, type, row) {
                    return currency(row.promo);
                }
            },
            {
                "data": "total_hpp",
                render: function(data, type, row) {
                    return currency(row.total_hpp);
                }
            },
            {
                "data": "sub_total",
                render: function(data, type, row) {
                    return currency(row.sub_total);
                }
            },
            {
                render: function(data, type, row) {
                    let profit = row.sub_total-row.total_hpp
                    return currency(profit);
                }
            },
        ],
    });

    // NUMBERING ROW
    t.on( 'order.dt search.dt', function () {
        t.column(0, {search:'applied', order:'applied'}).nodes().each( function (cell, i) {
            cell.innerHTML = i+1;
        } );
    } ).draw();
   };
});
</script>
