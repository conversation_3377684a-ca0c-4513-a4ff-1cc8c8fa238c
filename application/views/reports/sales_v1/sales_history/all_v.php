<style type="text/css">
    .tooltip-inner {
    white-space: pre-wrap;
}
</style>
<!-- <div class="row" id="nav-mobile-sukses">    
    <div class="col-lg-1 col-sm-12">
        <button class="btn btn-info btn-block" type="button" data-toggle="collapse" data-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample">filter Here</button>
    </div>
    <div class="col-lg-11 col-sm-12">
        <div class="collapse" id="collapseExample">
            <div class="well" style="background-color: #272a2b; margin-bottom: 0px;padding-top: 1px;padding-bottom: 1px;">
                <div class="row">
                    
                        <div class="btn-group">
                            <button type="button" class=" btn btn-info btn-block" id="dateAll" >
                                <span> 
                                    <i class="glyphicon glyphicon-calendar"></i> Date
                                </span>
                                <i class="caret"></i>
                            </button>
                        </div>
                    
                    
                        <div class="btn-group">
                            <select class="form-control btn btn-info btn-block" id="select_outlet_all" name="select_outlet" >
                                <?php foreach ($form_select_outlet as $a): ?>                   
                                    <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
                                <?php endforeach ?>
                                <option value="0">ALL Outlet</option>
                            </select>                                   
                        </div>
                    
                    
                        <div class="btn-group">
                            <select class="form-control btn btn-info btn-block"  id="select_shift_all" name="select_shift">
                                <option value="0" selected="">ALL Shif</option>
                                <?php foreach ($form_select_shift as $a): ?>                    
                                    <option value="<?=$a->shift_id ?>"><?=htmlentities($a->name) ?></option>
                                <?php endforeach ?>
                            </select>                                   
                        </div>   
                    
                        <div class="btn-group">
                            <select class="form-control btn btn-info"  id="data_type" name="data_type">
                                <option value="0">By Shift</option>
                                <option value="1">By Date</option>
                            </select>                                   
                        </div>
                        <div class="btn-group">
                            <button class="btn btn-primary btn-block" type="button">Apply</button>                                
                        </div>
                    
                </div>
            </div>
        </div>
    </div>
</div> -->

<!-- <div class="row">
    <div class="col-sm-12">
         <div class="panel-group" style="margin-bottom: 0px">
            <div class="panel panel-uniq" style="border: none;">
                <div class="panel-heading" style="background-color: #272b2c">
                    <a class="panel-title" data-toggle="collapse" href="#collapse1" aria-expanded="true" style="color: #fff">
                        <div>
                            <span class="fa fa-filter"></span> Filter Here
                        </div>
                    </a>
                </div>
                <div id="collapse1" class="panel-collapse collapse out" aria-expanded="true" style="">
                    <div class="panel-body" style="background-color: #272a2b;">
                        <div style="float: right;">
                            <div class="btn-group">
                                <button type="button" class=" btn btn-info" id="dateAll" >
                                    <span> 
                                        <i class="glyphicon glyphicon-calendar"></i> Date
                                    </span>
                                    <i class="caret"></i>
                                </button>
                            </div>
                            <div class="btn-group">
                                <select class="form-control btn btn-info" id="select_outlet_all" name="select_outlet" >
                                    <?php foreach ($form_select_outlet as $a): ?>                   
                                        <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
                                    <?php endforeach ?>
                                    <option value="0">ALL Outlet</option>
                                </select>                                   
                            </div>
                            <div class="btn-group">
                                <select class="form-control btn btn-info"  id="select_shift_all" name="select_shift">
                                    <option value="0" selected="">ALL Shif</option>
                                    <?php foreach ($form_select_shift as $a): ?>                    
                                        <option value="<?=$a->shift_id ?>"><?=htmlentities($a->name) ?></option>
                                    <?php endforeach ?>
                                </select>                                   
                            </div>
                            <div class="btn-group">
                                <select class="form-control btn btn-info"  id="select_shift_all" name="select_shift">
                                    <option value="0" selected="">ALL Shif</option>
                                    <?php foreach ($form_select_shift as $a): ?>                    
                                        <option value="<?=$a->shift_id ?>"><?=htmlentities($a->name) ?></option>
                                    <?php endforeach ?>
                                </select>                                   
                            </div>
                            <div class="btn-group">
                                <select class="form-control btn btn-info"  id="select_shift_all" name="select_shift">
                                    <option value="0" selected="">ALL Shif</option>
                                    <?php foreach ($form_select_shift as $a): ?>                    
                                        <option value="<?=$a->shift_id ?>"><?=htmlentities($a->name) ?></option>
                                    <?php endforeach ?>
                                </select>                                   
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> -->
   
<div class="row pull-right" style="margin-bottom: 5px" id="nav-dekstop-sukses"> 
	<div class="col-sm-12" >
		<div class="btn-group">
            <button type="button" class=" btn btn-info" id="dateAll" >
                <span> 
                    <i class="glyphicon glyphicon-calendar"></i> Date
                </span>
            </button>
        </div>
        <div class="btn-group">
            <select class="form-control btn btn-info" id="select_outlet_all" name="select_outlet" >
                <option value="0">All Outlet</option>
                <?php foreach ($form_select_outlet as $a): ?>                   
                    <option value="<?=$a->outlet_id ?>"><?=htmlentities($a->name) ?></option>
                <?php endforeach ?>
            </select>                                   
        </div>
        <div class="btn-group">
            <select class="form-control btn btn-info"  id="select_shift_all" name="select_shift">                
                <option value="0">All Shift</option>
                <?php foreach ($form_select_shift as $a): ?>                    
                    <option value="<?=$a->shift_id ?>"><?=htmlentities($a->name) ?></option>
                <?php endforeach ?>
            </select>                                   
        </div>
        <div class="btn-group">
            <select class="form-control btn btn-info"  id="data_type_trans" name="data_type">
                <option value="1">By Date</option>
                <option value="0">By Shift</option>
            </select>                                   
        </div>
        <div class="btn-group">
            <button class="btn btn-primary btn-block" id="btnTransaksi" type="button">Apply</button>                                
        </div>
    </div>
</div>
<div class="row">
    <div class="col-md-12 col-sm-12 col-xs-12">
        <!-- <table id="example" class="stripe row-border order-column" cellspacing="0" width="100%"> -->
        <table id="all" class="table  table-striped table-responsive table-report" cellspacing="0" width="100%">
            <thead>
                <tr> 
                    <th>No</th>
                    <th>No Order</th>
                    <th>Outlet</th>
                    <th>Shift</th>
                    <th>Tanggal </th>
                    <th>Status</th>
                    <th>Pembayaran</th>
                    <!-- <th>Catatan</th> -->
                    <th>PAX</th>
                    <th>Operator</th>
                    <th>Sub Total</th>
                    <th>Discount total</th>
                    <th>Keterangan</th>
                    <th>Voucher total</th>
                    <th>Keterangan </th>
                    <th>TAX</th>
                    <th>Servis</th>
                    <th>Grand Total</th>
                </tr>
            </thead>  
                <tfoot style="background: #b7b6b6">
                    <tr>
                        <th colspan="7">TOTAL</th>
                        <!-- <th></th> -->
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                        <th></th>
                    </tr>
                </tfoot>
        </table>
    </div>
</div>
<script type="text/javascript">

$(document).ready(function() {
    var d = new Date();
    var e=d.getTimezoneOffset();
    var timeZone = e*60*-1;
    var start = moment();
    var end = moment();
    var outlet=$("#select_outlet_all").val();
    var shift=0;
    var startDate =moment().format('YYYY-MM-DD');
    var endDate= moment().format('YYYY-MM-DD');
    $('#dateAll span').html(start.format('MMMM D, YYYY'));

    // onclick btn transaksi
    $('#btnTransaksi').on('click',function(){
        tableAll(shift,outlet,timeZone,startDate,endDate,$('#data_type_trans').val());
    }); 

    //select shift start
   $('#select_shift_all').on('change',function(){
        shift=$(this).val();
    });

   //select outlet start
   $('#select_outlet_all').focus(function(){
        outlet=$(this).val();
    }).bind('change',function(){                   
        if ($(this).val()==0) {
            swal({
                title: 'Warning!',
                text: "Proses ini mungkin membutuhkan sedikit waktu, pastikan koneksi internet anda dalam keadaan baik",
                type: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Lanjut'
            }).then((result) => {
                if (result.value) {
                    outlet=$(this).val(); 
                }else  {
                    $(this).val(outlet);
                    return false;
                }
            })
        }else{
            outlet=$(this).val(); 
        } 

    });

        $('#dateAll').daterangepicker({
                ranges: {
                    'Today': [moment(), moment()],
                    'Yesterday': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                    'Last 7 Days': [moment().subtract(6, 'days'), moment()],
                    'Last 30 Days': [moment().subtract(29, 'days'), moment()],
                    'This Month': [moment().startOf('month'), moment().endOf('month')],
                    'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
                },
                startDate: moment(),
                endDate: moment(),
                opens: "left"
            },
            function(start, end) {
                $('#dateAll span').html(start.format('MMMM D, YYYY') + ' - ' + end.format('MMMM D, YYYY'));
                 startDate=start.format('YYYY-MM-DD');
                 endDate=end.format('YYYY-MM-DD');
            }
        );
   


   
function tableAll(shift,outlet,timeZone,startDate,endDate,dataType){    
$('#all').DataTable().destroy(); //hapus data table 
$("#all > tbody > tr").remove(); //hapus content datatable      
$.fn.dataTableExt.oApi.fnPagingInfo = function(oSettings)
    {
        return {
            "iStart": oSettings._iDisplayStart,
            "iEnd": oSettings.fnDisplayEnd(),
            "iLength": oSettings._iDisplayLength,
            "iTotal": oSettings.fnRecordsTotal(),
            "iFilteredTotal": oSettings.fnRecordsDisplay(),
            "iPage": Math.ceil(oSettings._iDisplayStart / oSettings._iDisplayLength),
            "iTotalPages": Math.ceil(oSettings.fnRecordsDisplay() / oSettings._iDisplayLength)
        };
    };

    //to format number from footer
    var numFormat = $.fn.dataTable.render.number( formatAngka(),  ).display;
    var formatDes = $.fn.dataTable.render.number( '\.', ',', 2,  ).display;

    var t = $("#all").dataTable({

        dom: 'l<"toolbar">Bfrtip', //buat custom toolbar
         initComplete: function() {
            var api = this.api();
            $('#all_filter input')
                    .on('.DT')
                    .on('keyup.DT', function(e) {
                        api.search(this.value).draw();
            });
        },
        buttons: [
            {
                extend: 'collection',
                text: 'EXPORT',
                buttons: [
                    {
                        extend: 'pdfHtml5',
                        orientation: 'landscape',
                        pageSize: 'legal',
                        title: "REPORT SALES HISTORY TRANSAKSI",
                        filename:"Report_sales_history_transaksi",
                        // footer:true,
                        exportOptions: {
                            columns: [ 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16 ]
                        }
                        
                    },
                    {
                        extend: 'excelHtml5',
                        title:"Report_sales_history_transaksi",
                        messageTop: "REPORT SALES HISTORY TRANSAKSI",
                        // footer:true,
                        exportOptions: {                           
                            columns: [ 1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16 ],                            
                            format: {
                                body: function ( data, row, column, node ) {
                                    data = $('<p>' + data + '</p>').text();
                                    return $.isNumeric(data.replace( /[<?php echo $this->session->userdata('currency_symbol') ?>.,]/g, '' )) ? data.replace( /[<?php echo $this->session->userdata('currency_symbol') ?>.,]/g, '' ) : data;
                                }                                    
                                
                            }
                        }
                            
                    },
            
                ],
                           
            }
        ],
        //sorting curent   
        columnDefs: [
           { type: 'formatted-num', targets: [7,9,10,12,14,15,16] }
        ], 

        "footerCallback": function ( row, data, start, end, display ) {
            var api = this.api(), data;
 
            // Remove the formatting to get integer data
            var intVal = function ( i ) {
                return typeof i === 'string' ?
                    i.replace( /[<?php echo $this->session->userdata('currency_symbol') ?>.,]/g, '' )*1 :
                    typeof i === 'number' ?
                        i : 0;
            };
 
            // Total over all pages
           
            
            
            pax = api.column( 7, {page:'current'} ).data().reduce( function (a, b) {
                return intVal(a) + intVal(b);
            }, 0 );
            sub_total = api.column( 9, {page:'current'} ).data().reduce( function (a, b) {
                return intVal(a) + intVal(b);
            }, 0 );
            discountTotal = api.column( 10, {page:'current'} ).data().reduce( function (a, b) {
                return intVal(a) + intVal(b);
            }, 0 );
            voucherTotal = api.column( 12, {page:'current'} ).data().reduce( function (a, b) {
                return intVal(a) + intVal(b);
            }, 0 );
            tax = api.column( 14, {page:'current'} ).data().reduce( function (a, b) {
                return intVal(a) + intVal(b);
            }, 0 );
            service = api.column( 15, {page:'current'} ).data().reduce( function (a, b) {
                return intVal(a) + intVal(b);
            }, 0 );
            grand_total = api.column( 16, {page:'current'} ).data().reduce( function (a, b) {
                return intVal(a) + intVal(b);
            }, 0 );

 
            // Update footer
            
            
            $( api.column( 7 ).footer() ).html(numFormat(pax) );
            $( api.column( 9 ).footer() ).html(formatUang(numFormat(sub_total)));
            $( api.column( 10 ).footer() ).html(formatUang(numFormat(discountTotal)));
            $( api.column( 12 ).footer() ).html(formatUang(numFormat(voucherTotal)));
            $( api.column( 14 ).footer() ).html(formatUang(numFormat(tax)));
            $( api.column( 15 ).footer() ).html(formatUang(numFormat(service)));
            $( api.column( 16 ).footer() ).html(formatUang(numFormat(grand_total)));
        },


        //to table scroll left top
            scrollY:        "440px",
            scrollX:        true,
            scrollCollapse: true,
            paging:         true,
            ordering:       true,
            fixedColumns:   {
                leftColumns: 4,
                // rightColumns:1
            },
            oLanguage: {
                sProcessing: loading_show(),
            },
            initComplete: function() {
                loading_hide();
            },

        processing: false,
        serverSide: false,
        autoWidth: true,
        ajax: {
            "url": "<?=$ajaxActionDatatableAll ?>"+shift+"/"+outlet+"/"+timeZone+"/"+startDate+"/"+endDate+"/"+dataType, 
            "type": "post",
            "contentType": "application/json; charset=utf-8",
            "cache": false,
        },
        columns: [
            {"data": null},
            {"data": "nomor_nota"},
            {"data": "outlet_name"},
            {"data": "shift_name"},
            {"data": "tanggal"},
            {"data": "status"},
            {
                // "data": "pembayaran"
                render: function(data, type, row) {
                    let payment = []
                    let method =[]
                    $.each(row.pembayaran, function(index, val) {
                        payment.push(index,formatUang(numFormat(val)))
                        method.push(index)

                    });
                    let tes = payment.toString().replace(/(.+?,.+?),\s*/g,'$1\n');                    
                    return '<span data-toggle="tooltip" data-placement="left" title="" data-original-title="'+tes.replace(/,/g,' : ')+'">'+method+'</span>'
                }
            },
            // {"data": "pembayaran"},
            {"data": "pax"},
            {"data": "employee"},
            {"data": "sub_total"},
            {"data": "total_discount"},
            {"data": "keterangan_discount"},
            {"data": "total_voucher"},
            {"data": "keterangan_voucherS"},
            {"data": "tax"},
            {"data": "service"},
            { "data": "grand_total"},            
        ], order: [[ 4, "asc" ]],


        rowCallback: function(row, data, iDisplayIndex) {
           
            var info = this.fnPagingInfo();
            var page = info.iPage;
            var length = info.iLength;
            var index = page * length + (iDisplayIndex + 1);
            $('td:eq(0)', row).html(index);
        }

    });
   }; 
});
</script>